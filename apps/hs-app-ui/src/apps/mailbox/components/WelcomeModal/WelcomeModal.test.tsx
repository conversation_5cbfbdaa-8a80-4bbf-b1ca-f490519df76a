import { isInboxWelcomeModalEnabled } from '@helpscout/experiment-kit'
import { InboxWelcomeModal } from '@helpscout/growth-kit'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { render, screen, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'

import {
  getHsAppLocalStorageValue,
  setHsAppLocalStorageValue,
} from 'shared/utils/HsAppLocalStorage/hsAppLocalStorage'
import { DISMISSED_ELEMENTS_KEY } from 'shared/utils/dismissedElements/constants'

import WelcomeModal from './WelcomeModal'

// Mock Portal component to simplify testing
jest.mock('hsds/components/portal', () => {
  return function MockPortal({ children }: { children: React.ReactNode }) {
    return <div data-testid="portal">{children}</div>
  }
})

// Mock external dependencies
jest.mock('@helpscout/experiment-kit', () => ({
  isInboxWelcomeModalEnabled: jest.fn(),
}))

jest.mock('@helpscout/growth-kit', () => ({
  InboxWelcomeModal: jest.fn(),
}))

jest.mock('shared/utils/HsAppLocalStorage/hsAppLocalStorage', () => ({
  getHsAppLocalStorageValue: jest.fn(),
  setHsAppLocalStorageValue: jest.fn(),
}))

const mockIsInboxWelcomeModalEnabled = isInboxWelcomeModalEnabled as jest.Mock
const mockInboxWelcomeModal = InboxWelcomeModal as jest.Mock
const mockGetHsAppLocalStorageValue = getHsAppLocalStorageValue as jest.Mock
const mockSetHsAppLocalStorageValue = setHsAppLocalStorageValue as jest.Mock

// Mock InboxWelcomeModal to render a testable component
mockInboxWelcomeModal.mockImplementation(({ onClose, baseCdnPath }) => {
  if (!onClose) return null
  return (
    <div data-testid="inbox-welcome-modal">
      <h1>Welcome to your inbox!</h1>
      <p>Base CDN Path: {baseCdnPath || ''}</p>
      <button onClick={onClose} data-testid="close-modal">
        Close
      </button>
    </div>
  )
})

const renderWelcomeModal = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
      },
    },
  })

  return render(
    <QueryClientProvider client={queryClient}>
      <WelcomeModal />
    </QueryClientProvider>
  )
}

describe('WelcomeModal', () => {
  beforeEach(() => {
    jest.clearAllMocks()

    // Set up default window.hsGlobal
    global.window.hsGlobal = {
      companyId: 1,
      memberId: 1,
      features: {},
      namespace: 'test',
      pusherAppKey: 'test',
      pusherCluster: 'test',
      pusher: {
        namespace: 'test',
        pusherAppKey: 'test',
        pusherCluster: 'test',
      },
      timezone: 'test',
      timeFormat: 1,
      memberPermissions: {
        manageAccount: true,
      },
      imagePath: 'some-image-path',
    }
  })

  afterEach(() => {
    // @ts-ignore - Cleanup after tests
    delete global.window.hsGlobal
  })

  describe('when feature is enabled and modal not dismissed', () => {
    beforeEach(() => {
      mockIsInboxWelcomeModalEnabled.mockResolvedValue(true)
      mockGetHsAppLocalStorageValue.mockReturnValue({})
    })

    it('should render the modal', async () => {
      renderWelcomeModal()

      await waitFor(() => {
        expect(screen.getByTestId('inbox-welcome-modal')).toBeInTheDocument()
      })

      expect(screen.getByText('Welcome to your inbox!')).toBeInTheDocument()
    })

    it('should pass the correct baseCdnPath from window.hsGlobal', async () => {
      renderWelcomeModal()

      await waitFor(() => {
        expect(
          screen.getByText('Base CDN Path: some-image-path')
        ).toBeInTheDocument()
      })
    })

    it('should use empty string as baseCdnPath when window.hsGlobal is not available', async () => {
      // @ts-ignore - Intentionally deleting hsGlobal for testing
      delete global.window.hsGlobal

      renderWelcomeModal()

      await waitFor(() => {
        expect(screen.getByText('Base CDN Path:')).toBeInTheDocument()
      })
    })
  })

  describe('when feature is disabled', () => {
    beforeEach(() => {
      mockIsInboxWelcomeModalEnabled.mockResolvedValue(false)
    })

    it('should not render the modal', async () => {
      renderWelcomeModal()

      await waitFor(() => {
        expect(
          screen.queryByTestId('inbox-welcome-modal')
        ).not.toBeInTheDocument()
      })
    })
  })

  describe('when modal is already dismissed', () => {
    beforeEach(() => {
      mockIsInboxWelcomeModalEnabled.mockResolvedValue(true)
      mockGetHsAppLocalStorageValue.mockReturnValue({
        'inbox-welcome-modal': true,
      })
    })

    it('should not render the modal', async () => {
      renderWelcomeModal()

      await waitFor(() => {
        expect(
          screen.queryByTestId('inbox-welcome-modal')
        ).not.toBeInTheDocument()
      })
    })
  })

  describe('localStorage interaction', () => {
    beforeEach(() => {
      mockIsInboxWelcomeModalEnabled.mockResolvedValue(true)
      mockGetHsAppLocalStorageValue.mockReturnValue({})
    })

    it('should read dismissed elements from localStorage on initialization', async () => {
      renderWelcomeModal()

      await waitFor(() => {
        expect(mockGetHsAppLocalStorageValue).toHaveBeenCalledWith(
          DISMISSED_ELEMENTS_KEY
        )
      })
    })

    it('should handle invalid localStorage data gracefully', async () => {
      mockGetHsAppLocalStorageValue.mockReturnValue('invalid-data')

      renderWelcomeModal()

      await waitFor(() => {
        expect(screen.getByTestId('inbox-welcome-modal')).toBeInTheDocument()
      })
    })
  })

  describe('modal close functionality', () => {
    beforeEach(() => {
      mockIsInboxWelcomeModalEnabled.mockResolvedValue(true)
      mockGetHsAppLocalStorageValue.mockReturnValue({})
    })

    it('should close modal and persist dismissal when onClose is called', async () => {
      renderWelcomeModal()

      await waitFor(() => {
        expect(screen.getByTestId('inbox-welcome-modal')).toBeInTheDocument()
      })

      const closeButton = screen.getByTestId('close-modal')
      userEvent.click(closeButton)

      expect(mockSetHsAppLocalStorageValue).toHaveBeenCalledWith(
        DISMISSED_ELEMENTS_KEY,
        {
          'inbox-welcome-modal': true,
        }
      )

      await waitFor(() => {
        expect(
          screen.queryByTestId('inbox-welcome-modal')
        ).not.toBeInTheDocument()
      })
    })

    it('should preserve existing dismissed elements when closing modal', async () => {
      const existingDismissed = { 'other-modal': true }
      mockGetHsAppLocalStorageValue.mockReturnValue(existingDismissed)

      renderWelcomeModal()

      await waitFor(() => {
        expect(screen.getByTestId('inbox-welcome-modal')).toBeInTheDocument()
      })

      const closeButton = screen.getByTestId('close-modal')
      userEvent.click(closeButton)

      expect(mockSetHsAppLocalStorageValue).toHaveBeenCalledWith(
        DISMISSED_ELEMENTS_KEY,
        {
          'other-modal': true,
          'inbox-welcome-modal': true,
        }
      )
    })
  })

  describe('error handling', () => {
    beforeEach(() => {
      jest.spyOn(console, 'error').mockImplementation(() => {})
    })

    afterEach(() => {
      jest.restoreAllMocks()
    })

    it('should handle experiment-kit errors gracefully and not show modal', async () => {
      const error = new Error('Experiment kit failed')
      mockIsInboxWelcomeModalEnabled.mockRejectedValue(error)

      renderWelcomeModal()

      await waitFor(() => {
        expect(console.error).toHaveBeenCalledWith(
          'Error in welcome-modal-state queryFn:',
          error
        )
      })

      expect(
        screen.queryByTestId('inbox-welcome-modal')
      ).not.toBeInTheDocument()
    })

    it('should handle localStorage errors gracefully', async () => {
      mockIsInboxWelcomeModalEnabled.mockResolvedValue(true)
      mockGetHsAppLocalStorageValue.mockImplementation(() => {
        throw new Error('localStorage failed')
      })

      renderWelcomeModal()

      await waitFor(() => {
        expect(console.error).toHaveBeenCalledWith(
          'Error in welcome-modal-state queryFn:',
          expect.any(Error)
        )
      })

      expect(
        screen.queryByTestId('inbox-welcome-modal')
      ).not.toBeInTheDocument()
    })
  })

  describe('integration tests', () => {
    beforeEach(() => {
      mockIsInboxWelcomeModalEnabled.mockResolvedValue(true)
      mockGetHsAppLocalStorageValue.mockReturnValue({})
    })

    it('should call experiment-kit to check if feature is enabled', async () => {
      renderWelcomeModal()

      await waitFor(() => {
        expect(mockIsInboxWelcomeModalEnabled).toHaveBeenCalled()
      })
    })
  })
})
