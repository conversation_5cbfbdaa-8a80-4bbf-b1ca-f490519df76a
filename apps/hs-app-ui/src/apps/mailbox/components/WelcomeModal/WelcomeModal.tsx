import { InboxWelcomeModal } from '@helpscout/growth-kit'

import { useWelcomeModal } from 'shared/utils/welcomeModal/welcomeModalUtils'

import Portal from 'hsds/components/portal'

/**
 * WelcomeModal component
 *
 * A wrapper for InboxWelcomeModal that renders it in a Portal to ensure
 * it's inserted directly into the document body, regardless of where
 * it's called in the component tree.
 *
 * Handles its own visibility state based on localStorage and provides
 * a callback to close and persist the user's preference.
 */
export default function WelcomeModal(): JSX.Element | null {
  const { showWelcomeModal, handleClose } = useWelcomeModal()

  if (!showWelcomeModal) return null

  return (
    <Portal>
      <InboxWelcomeModal
        onClose={handleClose}
        baseCdnPath={window.hsGlobal?.imagePath || ''}
      />
    </Portal>
  )
}
