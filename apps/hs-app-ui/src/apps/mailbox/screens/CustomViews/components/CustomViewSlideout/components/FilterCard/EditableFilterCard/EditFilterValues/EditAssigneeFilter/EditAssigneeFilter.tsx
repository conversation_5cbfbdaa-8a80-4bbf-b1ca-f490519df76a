import { useCallback, useEffect, useMemo, useState } from 'react'

import { useHsAppContext } from 'shared/hooks'

import { SCOPE_VIEWS_SLIDE_OUT } from 'apps/mailbox/App/constants/hotkeyScopes'
import { MAX_ASSIGNEES } from 'apps/mailbox/screens/CustomViews/constants/customView.constants'
import { Operator } from 'shared/constants/viewsOperators'

import {
  AssigneeNameUI,
  AvatarUI,
  ButtonUI,
  CardListUI,
  CardUI,
  IconButtonUI,
} from './EditAssigneeFilter.css'

import { UNASSIGNED_USER } from '../../../FilterCard.constants'
import CrossSmall from 'hsds/icons/cross-tiny'
import AddUsersModal from 'shared/components/AddUsersModal'
import { MailboxUser } from 'shared/types/global'

interface EditAssigneeFilterProps {
  onUpdate: (values: number[] | undefined) => void
  filterValue: number[] | undefined
  operator: Operator
}

const EditAssigneeFilter = ({
  onUpdate,
  filterValue,
  operator,
}: EditAssigneeFilterProps) => {
  const [isModalOpen, setIsModalOpen] = useState(false)
  const {
    appData: { shared },
  } = useHsAppContext()

  useEffect(() => {
    if (operator === Operator.NOT_SET && filterValue) {
      onUpdate(undefined)
    }
  }, [filterValue, onUpdate, operator])

  const handleOpenModal = (event: React.MouseEvent<HTMLButtonElement>) => {
    setIsModalOpen(true)
    event.stopPropagation()
  }

  const handleCloseModal = () => {
    setIsModalOpen(false)
  }

  const handleSelectUsers = (users: number[]) => {
    setIsModalOpen(false)

    onUpdate(users)
  }

  const handleRemoveUser = useCallback(
    (userId: number) => {
      if (!filterValue) return
      onUpdate(filterValue.map(Number).filter(user => user !== userId))
    },
    [filterValue, onUpdate]
  )

  const assignableUsers: MailboxUser[] = useMemo(() => {
    return [
      UNASSIGNED_USER,
      ...(shared?.mailboxUsers?.filter(user =>
        shared?.mailboxUsersWhoCanBeAssigned?.some(userId => userId === user.id)
      ) || []),
    ]
  }, [shared?.mailboxUsers, shared?.mailboxUsersWhoCanBeAssigned])

  const isValidValue = useCallback(
    (value: number) => assignableUsers.some(user => user.id === value),
    [assignableUsers]
  )

  useEffect(() => {
    if (!filterValue) return
    for (const userId of filterValue) {
      if (!isValidValue(userId)) {
        handleRemoveUser(userId)
      }
    }
  }, [filterValue, handleRemoveUser, isValidValue])

  if (operator === Operator.NOT_SET) {
    return null
  }

  const selectedUsers = filterValue
    ? assignableUsers.filter(user => filterValue.includes(user.id))
    : []

  return (
    <>
      <ButtonUI outlined size="lg" color="grey" onClick={handleOpenModal}>
        Choose
      </ButtonUI>
      {selectedUsers && selectedUsers?.length > 0 && (
        <CardListUI>
          {selectedUsers?.map(user =>
            user ? (
              <CardUI key={user.id}>
                <AvatarUI image={user.photoUrl} name={user.fullName} />
                <div>
                  <AssigneeNameUI>{user.fullName}</AssigneeNameUI>
                  {/* Role is not a value we currently have access to from appData mailboxUsers, there is a request out to BE to return this so once that is complete we can display the role below */}
                  {/* <AssigneeRoleUI>{user.role}</AssigneeRoleUI> */}
                </div>

                <IconButtonUI
                  seamless
                  icon={CrossSmall}
                  onClick={() => handleRemoveUser(user.id)}
                />
              </CardUI>
            ) : null
          )}
        </CardListUI>
      )}
      <AddUsersModal
        existingUsers={filterValue || []}
        isOpen={isModalOpen}
        onClose={handleCloseModal}
        onConfirm={handleSelectUsers}
        users={assignableUsers || []}
        title="Add Assignees"
        primaryButtonText="Add Assignees"
        maxAssignees={MAX_ASSIGNEES}
        hotkeyScopeToDisable={SCOPE_VIEWS_SLIDE_OUT}
      />
    </>
  )
}

export default EditAssigneeFilter
