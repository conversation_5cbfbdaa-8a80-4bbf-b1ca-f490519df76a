import { useRef, useState } from 'react'

import { useHsAppContext } from 'shared/hooks'

import { SCOPE_VIEWS_SLIDE_OUT } from 'apps/mailbox/App/constants/hotkeyScopes'
import { MAX_ASSIGNEES } from 'apps/mailbox/screens/CustomViews/constants/customView.constants'

import {
  Caret<PERSON>ownUI,
  CaretUpUI,
  VisibilityTogglerUI,
  visibilityMenuCss,
} from './VisibilitySelector.css'

import {
  CUSTOM,
  CUSTOM_VISIBILITY,
  visibilityMenuItems,
  visibilityOptions,
} from './VisibilitySelector.constants'
import DropList from 'hsds/components/drop-list'
import AddUsersModal from 'shared/components/AddUsersModal'

interface SelectedVisibility {
  label: string
  value: number
  group?: string
}

interface VisibilitySelectorProps {
  selectedVisibility: number | number[]
  handleSelectVisibility: (arg0: number | number[]) => void
  isNew: boolean
  viewCreatorId?: number
}

const visibilityDisplayName = (selectedVisibility: number | number[]) => {
  if (Array.isArray(selectedVisibility)) {
    // if the selected visibility is an array, its means custom visibility has been selected and the array will container user ids
    return CUSTOM
  } else {
    const thing = visibilityOptions.find(
      item => item.value === selectedVisibility
    )
    return thing?.label
  }
}

const VisibilitySelector = ({
  selectedVisibility,
  handleSelectVisibility,
  isNew,
  viewCreatorId,
}: VisibilitySelectorProps) => {
  const {
    appData: { shared },
  } = useHsAppContext()
  const previousSelectedVisibility = useRef(selectedVisibility)
  const [isCustomModalOpen, setIsCustomModalOpen] = useState(false)
  const [isDropListOpen, setIsDropListOpen] = useState(false)

  // We manage visibility dropdown state in this component because we need to know if the drop list is open/closed for showing the correct caret icon up or down state
  const handleVisibilityDropdownStateChange = (isOpen: boolean) => {
    setIsDropListOpen(isOpen)
  }

  // Closing the modal without saving should revert the visibility back to the previous selected visibility
  const handleCloseAddUserModal = () => {
    // This is needed to prevent resetting the visibility when the user clicks the Add Users button in the modal
    if (!isCustomModalOpen) {
      return
    }
    setIsCustomModalOpen(false)
    handleSelectVisibility(previousSelectedVisibility.current)
  }

  const handleConfirmAddUserModal = (selectedUsers: number[]) => {
    setIsCustomModalOpen(false)
    handleSelectVisibility(selectedUsers)
  }

  const renderToggler = (
    <VisibilityTogglerUI>
      {visibilityDisplayName(selectedVisibility)}{' '}
      {isDropListOpen ? <CaretUpUI /> : <CaretDownUI />}
    </VisibilityTogglerUI>
  )

  const handleOnSelect = (item: SelectedVisibility) => {
    // If there are already selected customer user ID, don't update the selected visibility so that we can show those user ID's as selected in the user selection modal
    const alreadyHasCustomSelectedUsers =
      Array.isArray(selectedVisibility) &&
      selectedVisibility.length > 0 &&
      item.value === CUSTOM_VISIBILITY

    if (alreadyHasCustomSelectedUsers) return

    handleSelectVisibility(item.value)
  }

  const handleOnItemSelect = ({
    event,
  }: {
    event: React.MouseEvent<HTMLSpanElement>
  }) => {
    // If the user selects the custom visibility option, we open the custom user selection modal
    if ((event.target as HTMLSpanElement).textContent === CUSTOM) {
      previousSelectedVisibility.current = selectedVisibility
      setIsCustomModalOpen(true)
      event.stopPropagation()
    }
  }

  const isCustomVisibilitySelected =
    Array.isArray(selectedVisibility) && selectedVisibility.length > 0
  const visibilitySelection = isCustomVisibilitySelected
    ? // custom label/value
      visibilityOptions[2]
    : visibilityOptions.find(option => option.value === selectedVisibility)

  const inboxUsers = shared?.mailboxUsers.filter(user =>
    isNew ? user.id !== shared?.member.id : user.id !== viewCreatorId
  )

  return (
    <>
      {/* // TODO: once we get back a views filters/info from the API, we may need
      to format the visibility ID's to a more friendly display name // we can
      use getVisibilityDisplayName for that from ExistingViewsCard.cells.tsx and
      we will likely want to move it to a more shared location within custom
      views */}
      <DropList
        id="visibility"
        name="visibility"
        required={true}
        selection={visibilitySelection}
        items={visibilityMenuItems}
        onOpenedStateChange={handleVisibilityDropdownStateChange}
        toggler={renderToggler}
        onSelect={handleOnSelect}
        selectionLimit={{ min: 1 }}
        menuCSS={visibilityMenuCss}
        onListItemSelectEvent={handleOnItemSelect}
      />
      <AddUsersModal
        isOpen={isCustomModalOpen}
        onClose={handleCloseAddUserModal}
        onConfirm={handleConfirmAddUserModal}
        users={inboxUsers || []}
        title="Add users to a Custom View"
        primaryButtonText={isNew ? 'Add Users' : 'Save'}
        existingUsers={
          Array.isArray(selectedVisibility) ? selectedVisibility : []
        }
        maxAssignees={MAX_ASSIGNEES}
        hotkeyScopeToDisable={SCOPE_VIEWS_SLIDE_OUT}
      />
    </>
  )
}

export default VisibilitySelector
