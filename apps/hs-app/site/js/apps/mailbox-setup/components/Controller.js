import HSController from '../../common/controllers/HSController'
import MailboxModel from '../../common/models/MailboxModel'
import PricingPlanModel from '../../common/models/PricingPlanModel'
import App from '../App'
import PersonalizationModel from '../models/PersonalizationModel'
import SegmentationModel from '../models/SegmentationModel'
import UserModel from '../models/UserModel'
import GetStartedView from '../views/GetStartedView'
import PersonalizationQuestionsView from '../views/PersonalizationQuestionsView'
import SegmentationQuestionsView from '../views/SegmentationQuestionsView'

module.exports = HSController.extend({
  initialize: function () {
    HSController.prototype.initialize.call(this)
    App.controller = this
    this.segmentation = new SegmentationModel(appData.segmentation)
    this.personalization = new PersonalizationModel({
      companyName: appData.segmentation.companyName,
      companyIndustry: appData.segmentation.companyIndustry,
      companyEmployeeCount: appData.segmentation.companyEmployeeCount,
      currentHelpDesk: appData.segmentation.currentHelpDesk,
      currentHelpDeskEmail: appData.segmentation.currentHelpDeskEmail,
      currentHelpDeskPhone: appData.segmentation.currentHelpDeskPhone,
      currentHelpDeskName: appData.segmentation.currentHelpDeskName,
    })
    App.data.plan = new PricingPlanModel(appData.plan)
    this.listenTo(App, 'forwarder-complete', this.onForwarderComplete)
  },
  index() {
    // If the company has an existing mailbox, redirect to it when they load the welcome page
    const fragment = App.data.mailbox
      ? 'new-mailbox/'
      : 'segmentation-questions/'
    App.appRouter.navigate(fragment, { trigger: true })
  },
  personalizationQuestions() {
    const view = new PersonalizationQuestionsView({
      model: this.personalization,
    })
    App.main.show(view)
  },
  segmentationQuestions() {
    const view = new SegmentationQuestionsView({
      model: this.segmentation,
    })
    App.main.show(view)
  },
  getStarted() {
    const view = new GetStartedView()
    App.main.show(view)
  },
  setupCurrentUser: function () {
    let currentUser
    if (appData.type === 'welcome') {
      currentUser = new UserModel({
        name: appData.member.fullName,
        email: appData.member.email,
        role: 'owner',
      })
    }
    return currentUser
  },
  fetchMailbox: function (mailboxId) {
    let doFetch = true
    if (App.data.mailbox && App.data.mailbox.get('id') == mailboxId) {
      doFetch = false
    }
    if (doFetch) {
      const model = new MailboxModel({ id: mailboxId })
      return model.fetch({
        success: function (serverModel, response) {
          App.data.mailbox = serverModel
          App.data.mxType = App.data.mxType || serverModel.attributes.mxType
        },
      })
    }
    return $.Deferred().resolve()
  },
  onForwarderComplete: function (mailboxId) {
    this.onSetupComplete(App.data.mailbox.id)
  },
  onSetupComplete: function (mailboxId) {
    // Ensure we have an App.data.mailbox
    this.fetchMailbox(mailboxId).then(() => {
      window.location.href = `/mailbox/${App.data.mailbox.get('slug')}`
    })
  },
})
