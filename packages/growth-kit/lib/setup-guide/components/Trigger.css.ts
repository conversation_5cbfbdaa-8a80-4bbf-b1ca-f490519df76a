import styled from 'styled-components'

import { getColor } from 'hsds/utils/color'
import { focusRing } from 'hsds/utils/mixins'

const breakpoint = '912px'

export const TriggerButton = styled.button`
  all: unset;
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 0 5px 0 5px;
  border-radius: 72px;
  height: 40px;
  color: white;
  background-color: ${getColor('cobalt.800')};
  cursor: pointer;
  transition: background-color 0.2s ease-in-out;

  @media screen and (min-width: ${breakpoint}) {
    padding: 0 9px 0 5px;
  }

  ${focusRing};
  --hsds-focus-ring-radius: 72px;

  &:hover {
    background-color: ${getColor('cobalt.900')};
  }

  &.is-active {
    background-color: ${getColor('cobalt.900')};
    outline: 2px solid rgb(59, 73, 129);
  }
`

export const TriggerContent = styled.span`
  display: none;

  @media screen and (min-width: ${breakpoint}) {
    display: inline-block;
    font-size: 12px;
    font-weight: 500;
  }
`
