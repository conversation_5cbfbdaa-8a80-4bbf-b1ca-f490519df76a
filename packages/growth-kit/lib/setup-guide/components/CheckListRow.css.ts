import styled from 'styled-components'

import { getColor } from 'hsds/utils/color'
import { focusRing } from 'hsds/utils/mixins'

export const CheckListRowChevron = styled.div`
  margin-right: -9px;
`

export const CheckListRowTitle = styled.div`
  font-weight: 500;
  color: ${getColor('charcoal.1200')};

  .is-completed & {
    color: ${getColor('charcoal.600')};
    text-decoration: line-through;
  }
`

export const CheckListRowBody = styled.div``

export const CheckListRowLayout = styled.button`
  all: unset;
  background: white;
  border-radius: 7px;
  box-sizing: border-box;

  &&& {
    padding: 12px 16px;
  }
  display: flex;
  gap: 6px;
  align-items: center;
  width: 100%;

  &:last-child {
    &&& {
      padding-bottom: 20px;
    }

    &:before {
      border-radius: 0 0 7px 7px;
    }
  }

  ${focusRing};
  --hsds-focus-ring-radius: 1px;
  --hsds-focus-ring-offset: -1px;

  &:not(:disabled) {
    cursor: pointer;
  }

  &:hover {
    ${CheckListRowTitle}, ${CheckListRowBody} {
      text-decoration: underline;
    }
  }

  &.is-variant-cobalt:not(:hover) {
    .c-Squircle {
      --hsds-squircle-background-color: ${getColor('cobalt.100')};
    }
  }

  &.is-variant-purple:not(:hover) {
    .c-Squircle {
      --hsds-squircle-background-color: ${getColor('purple.100')};
      --hsds-token-squircle-color-variant-purple-text: ${getColor(
        'purple.500'
      )};
    }
  }

  &.is-variant-purple {
    .c-Squircle {
      --hsds-token-squircle-color-variant-purple-text: ${getColor(
        'purple.500'
      )};
    }
  }

  ${CheckListRowChevron} {
    opacity: 0;
  }

  &:hover {
    ${CheckListRowChevron} {
      opacity: 1;
    }
  }
`

export const CheckListRowIcon = styled.div`
  margin-right: 3px;
`

export const CheckListRowContent = styled.div`
  display: flex;
  flex-direction: column;
  gap: 3px;
  font-size: 13px;
  padding-left: 6px;
  color: ${getColor('charcoal.800')};
  flex: 1;

  .is-completed:disabled & {
    color: ${getColor('charcoal.600')};
  }

  .is-completed:not(:disabled) & {
    color: ${getColor('cobalt.600')};
  }
`
