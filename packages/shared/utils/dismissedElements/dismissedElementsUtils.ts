import { z } from 'zod'

import {
  getHsAppLocalStorageValue,
  setHsAppLocalStorageValue,
} from 'shared/utils/HsAppLocalStorage/hsAppLocalStorage'

import { DISMISSED_ELEMENTS_KEY } from './constants'

const DismissedElementsSchema = z.record(z.boolean())

/**
 * Retrieves dismissed UI elements from local storage.
 *
 * @returns {Record<string, boolean>} An object mapping UI element IDs
 *   to their dismissal status. Returns an empty object if the data is
 *   not found or invalid.
 */
export function getDismissedElements(): Record<string, boolean> {
  const raw = getHsAppLocalStorageValue(DISMISSED_ELEMENTS_KEY)
  const parsed = DismissedElementsSchema.safeParse(raw)
  return parsed.success ? parsed.data : {}
}

/**
 * Sets a UI element as dismissed in local storage.
 *
 * @param elementKey - The key of the UI element to dismiss
 */
export function setElementDismissed(elementKey: string): void {
  const dismissedElements = getDismissedElements()
  dismissedElements[elementKey] = true
  setHsAppLocalStorageValue(DISMISSED_ELEMENTS_KEY, dismissedElements)
}

/**
 * Checks if a UI element has been dismissed.
 *
 * @param elementKey - The key of the UI element to check
 * @returns true if the element has been dismissed, false otherwise
 */
export function isElementDismissed(elementKey: string): boolean {
  const dismissedElements = getDismissedElements()
  return dismissedElements[elementKey] === true
}
