import {
  getHsAppLocalStorageValue,
  setHsAppLocalStorageValue,
} from 'shared/utils/HsAppLocalStorage/hsAppLocalStorage'

import { DISMISSED_ELEMENTS_KEY } from './constants'

import {
  getDismissedElements,
  isElementDismissed,
  setElementDismissed,
} from './dismissedElementsUtils'

jest.mock('shared/utils/HsAppLocalStorage/hsAppLocalStorage', () => ({
  getHsAppLocalStorageValue: jest.fn(),
  setHsAppLocalStorageValue: jest.fn(),
}))

const mockGetHsAppLocalStorageValue = getHsAppLocalStorageValue as jest.Mock
const mockSetHsAppLocalStorageValue = setHsAppLocalStorageValue as jest.Mock

describe('dismissedElementsUtils', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  describe('getDismissedElements', () => {
    it('should return empty object when localStorage is empty', () => {
      mockGetHsAppLocalStorageValue.mockReturnValue(null)

      const result = getDismissedElements()

      expect(result).toEqual({})
      expect(mockGetHsAppLocalStorageValue).toHaveBeenCalledWith(
        DISMISSED_ELEMENTS_KEY
      )
    })

    it('should return parsed data when localStorage contains valid data', () => {
      const mockData = { 'modal-1': true, 'modal-2': false }
      mockGetHsAppLocalStorageValue.mockReturnValue(mockData)

      const result = getDismissedElements()

      expect(result).toEqual(mockData)
    })

    it('should return empty object when localStorage contains invalid data', () => {
      mockGetHsAppLocalStorageValue.mockReturnValue('invalid-data')

      const result = getDismissedElements()

      expect(result).toEqual({})
    })
  })

  describe('setElementDismissed', () => {
    it('should add element to dismissed list', () => {
      const existingData = { 'modal-1': true }
      mockGetHsAppLocalStorageValue.mockReturnValue(existingData)

      setElementDismissed('modal-2')

      expect(mockSetHsAppLocalStorageValue).toHaveBeenCalledWith(
        DISMISSED_ELEMENTS_KEY,
        { 'modal-1': true, 'modal-2': true }
      )
    })

    it('should work with empty initial data', () => {
      mockGetHsAppLocalStorageValue.mockReturnValue({})

      setElementDismissed('modal-1')

      expect(mockSetHsAppLocalStorageValue).toHaveBeenCalledWith(
        DISMISSED_ELEMENTS_KEY,
        { 'modal-1': true }
      )
    })
  })

  describe('isElementDismissed', () => {
    it('should return true when element is dismissed', () => {
      mockGetHsAppLocalStorageValue.mockReturnValue({ 'modal-1': true })

      const result = isElementDismissed('modal-1')

      expect(result).toBe(true)
    })

    it('should return false when element is not dismissed', () => {
      mockGetHsAppLocalStorageValue.mockReturnValue({ 'modal-1': false })

      const result = isElementDismissed('modal-1')

      expect(result).toBe(false)
    })

    it('should return false when element is not in storage', () => {
      mockGetHsAppLocalStorageValue.mockReturnValue({})

      const result = isElementDismissed('modal-1')

      expect(result).toBe(false)
    })
  })
})
