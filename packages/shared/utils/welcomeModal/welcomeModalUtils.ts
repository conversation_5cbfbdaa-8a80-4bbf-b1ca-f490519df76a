import { isInboxWelcomeModalEnabled } from '@helpscout/experiment-kit'
import { useQuery, useQueryClient } from '@tanstack/react-query'

import { WELCOME_MODAL_KEY } from 'shared/utils/dismissedElements/constants'
import {
  getDismissedElements,
  setElementDismissed,
} from 'shared/utils/dismissedElements/dismissedElementsUtils'

/**
 * Combined hook that provides welcome modal state management and controls.
 *
 * @returns Object containing showWelcomeModal state, isWelcomeModalDismissed state, and handleClose function
 */
export const useWelcomeModal = () => {
  const queryClient = useQueryClient()

  const { data = { showWelcomeModal: false, isWelcomeModalDismissed: true } } =
    useQuery({
      queryKey: ['welcome-modal-state'],
      queryFn: async () => {
        try {
          const isEnabled = await isInboxWelcomeModalEnabled()
          if (!isEnabled) {
            return { showWelcomeModal: false, isWelcomeModalDismissed: true }
          }

          const dismissedElements = getDismissedElements()
          const isDismissed = dismissedElements[WELCOME_MODAL_KEY] === true

          return {
            showWelcomeModal: !isDismissed,
            isWelcomeModalDismissed: isDismissed,
          }
        } catch (error) {
          console.error('Error in welcome-modal-state queryFn:', error)
          return { showWelcomeModal: false, isWelcomeModalDismissed: true } // Safe defaults on error
        }
      },
      staleTime: Infinity,
      onError: () => {
        console.error('Failed to fetch welcome-modal-state')
      },
    })

  /**
   * Handles the welcome modal close event by hiding it and persisting the user's
   * preference in localStorage under the 'dismissed-ui-elements' category.
   * This ensures the modal won't be shown again on subsequent visits.
   */
  const handleClose = (): void => {
    setElementDismissed(WELCOME_MODAL_KEY)
    queryClient.setQueryData(['welcome-modal-state'], {
      showWelcomeModal: false,
      isWelcomeModalDismissed: true,
    })
  }

  return {
    showWelcomeModal: data.showWelcomeModal,
    isWelcomeModalDismissed: data.isWelcomeModalDismissed,
    handleClose,
  }
}
