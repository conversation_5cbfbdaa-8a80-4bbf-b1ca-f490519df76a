import { trpc } from '../../../../utils/trpc'

import { useNoty } from '../../../../hooks'

export function useAddImprovement(
  beaconId: string,
  handleSuccess: (id: number) => void
) {
  const { showSuccessNoty } = useNoty()

  const patchBeacon = trpc.beacons.patchBeacon.useMutation({
    onSuccess: (_, variables) => {
      const improvementId = variables.value[0]

      showSuccessNoty('Improvement added')
      handleSuccess(improvementId)
    },
  })

  const saveImprovement = trpc.externalSources.createExternalSource.useMutation(
    {
      onSuccess: (response: { id: number }) => {
        patchBeacon.mutate({
          beaconId,
          op: 'add',
          path: '/ai/snippetIds',
          value: [response.id],
        })
      },
    }
  )

  return {
    add: saveImprovement.mutate,
    isAddingImprovement: saveImprovement.isLoading || patchBeacon.isLoading,
  }
}
