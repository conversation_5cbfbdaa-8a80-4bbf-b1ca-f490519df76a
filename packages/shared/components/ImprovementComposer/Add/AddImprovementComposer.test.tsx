import nock from 'nock'
import { ReactNode } from 'react'

import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { render, screen, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'

import { TrpcProvider, createTrpcClient } from '../../../utils/trpc'

import { waitForRequestsToFinish } from '../../../testUtils/async-utils'
import HsApp from '../../HsApp'
import { AddImprovementComposer } from './AddImprovementComposer'

// Mock global values for TRPC
Object.assign(window.hsGlobal, {
  hsAppUiServerUrl: 'http://localhost',
  trpcToken: 'test-token',
  memberId: 789,
  companyId: 1,
})

const TestWrapper = ({ children }: { children: ReactNode }) => {
  const trpcClient = createTrpcClient()
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
      },
      mutations: {
        retry: false,
      },
    },
  })

  return (
    <TrpcProvider queryClient={queryClient} client={trpcClient}>
      <QueryClientProvider client={queryClient}>
        <HsApp>
          <div>{children}</div>
        </HsApp>
      </QueryClientProvider>
    </TrpcProvider>
  )
}

const defaultProps = {
  isWideLayout: false,
  onClose: jest.fn(),
  conversationId: 123,
  beaconId: 'beacon-456',
}

describe('ImprovementComposer', () => {
  beforeEach(() => {
    nock.cleanAll()
  })
  afterEach(() => {
    nock.cleanAll()
    jest.clearAllMocks()
  })

  test('renders initial state with correct content', () => {
    render(
      <TestWrapper>
        <AddImprovementComposer {...defaultProps} />
      </TestWrapper>
    )

    expect(
      screen.getByText(
        'Help AI Answers improve future responses on this topic.'
      )
    ).toBeInTheDocument()
    expect(
      screen.getByPlaceholderText('Provide additional context')
    ).toBeInTheDocument()
    expect(screen.getByLabelText('Generate improvement')).toBeInTheDocument()
  })

  test('calls onClose when close button is clicked', () => {
    render(
      <TestWrapper>
        <AddImprovementComposer {...defaultProps} />
      </TestWrapper>
    )

    const closeButton = screen.getByTestId('improvement-close-button')
    userEvent.click(closeButton)

    expect(defaultProps.onClose).toHaveBeenCalledTimes(1)
  })

  test('calls onClose when Escape key is pressed', () => {
    render(
      <TestWrapper>
        <AddImprovementComposer {...defaultProps} />
      </TestWrapper>
    )

    userEvent.keyboard('{Escape}')

    expect(defaultProps.onClose).toHaveBeenCalledTimes(1)
  })

  test('does not call onClose when other keys are pressed', () => {
    render(
      <TestWrapper>
        <AddImprovementComposer {...defaultProps} />
      </TestWrapper>
    )

    userEvent.keyboard('{Enter}')
    userEvent.keyboard('a')
    userEvent.keyboard('{Tab}')

    expect(defaultProps.onClose).not.toHaveBeenCalled()
  })

  test('removes event listener on unmount', () => {
    const removeEventListenerSpy = jest.spyOn(document, 'removeEventListener')
    const { unmount } = render(
      <TestWrapper>
        <AddImprovementComposer {...defaultProps} />
      </TestWrapper>
    )

    unmount()

    expect(removeEventListenerSpy).toHaveBeenCalledWith(
      'keydown',
      expect.any(Function)
    )
    removeEventListenerSpy.mockRestore()
  })

  test('renders close button with correct aria-label', () => {
    render(
      <TestWrapper>
        <AddImprovementComposer {...defaultProps} />
      </TestWrapper>
    )

    const closeButton = screen.getByTestId('improvement-close-button')
    expect(closeButton).toHaveAttribute('aria-label', 'Discard improvement')
  })

  test('handles user input and enables/disables generate button', () => {
    render(
      <TestWrapper>
        <AddImprovementComposer {...defaultProps} />
      </TestWrapper>
    )

    const input = screen.getByPlaceholderText('Provide additional context')
    const generateButton = screen.getByLabelText('Generate improvement')

    expect(generateButton).toBeDisabled()

    userEvent.type(input, 'This needs improvement')
    expect(generateButton).toBeEnabled()

    userEvent.clear(input)
    expect(generateButton).toBeDisabled()
  })

  test('submits form and shows loading state', async () => {
    nock('http://localhost')
      .post(RegExp('generateImprovement'))
      .delay(100)
      .reply(200, {
        result: {
          data: {
            json: {
              title: 'Generated Title',
              text: 'Generated improvement text',
            },
          },
        },
      })

    render(
      <TestWrapper>
        <AddImprovementComposer {...defaultProps} />
      </TestWrapper>
    )

    const input = screen.getByPlaceholderText('Provide additional context')
    const generateButton = screen.getByLabelText('Generate improvement')

    userEvent.type(input, 'This needs improvement')
    userEvent.click(generateButton)

    expect(input).toHaveValue('')

    await waitFor(() => {
      expect(screen.getByTestId('purple-dot')).toBeInTheDocument()
    })

    await waitFor(
      () => {
        expect(screen.queryByTestId('purple-dot')).not.toBeInTheDocument()
      },
      { timeout: 3000 }
    )
  })

  test('displays generated content after successful API call', async () => {
    nock('http://localhost')
      .post(RegExp('generateImprovement'))
      .reply(200, {
        result: {
          data: {
            json: {
              title: 'Generated Title',
              text: 'Generated improvement text',
            },
          },
        },
      })

    render(
      <TestWrapper>
        <AddImprovementComposer {...defaultProps} />
      </TestWrapper>
    )

    const input = screen.getByPlaceholderText('Provide additional context')
    userEvent.type(input, 'This needs improvement')
    userEvent.click(screen.getByLabelText('Generate improvement'))

    await waitFor(() => {
      expect(screen.getByText('Generated Title')).toBeInTheDocument()
    })

    expect(screen.getByText('Generated improvement text')).toBeInTheDocument()
    expect(screen.getByText('This needs improvement')).toBeInTheDocument()
    expect(
      screen.getByText(
        "For clarity I've re-written the improvement. You can edit this later if needed:"
      )
    ).toBeInTheDocument()
    expect(screen.getByText('Save Improvement')).toBeInTheDocument()
    expect(screen.getByText('Retry')).toBeInTheDocument()
  })

  test('changes placeholder text in generated state', async () => {
    nock('http://localhost')
      .post(RegExp('generateImprovement'))
      .reply(200, {
        result: {
          data: {
            json: {
              title: 'Generated Title',
              text: 'Generated improvement text',
            },
          },
        },
      })

    render(
      <TestWrapper>
        <AddImprovementComposer {...defaultProps} />
      </TestWrapper>
    )

    const input = screen.getByPlaceholderText('Provide additional context')
    userEvent.type(input, 'This needs improvement')
    userEvent.click(screen.getByLabelText('Generate improvement'))

    await waitFor(() => {
      expect(screen.getByText('Generated Title')).toBeInTheDocument()
    })

    expect(
      screen.getByPlaceholderText('Share additional context')
    ).toBeInTheDocument()
  })

  test('handles retry functionality', async () => {
    let callCount = 0
    nock('http://localhost')
      .post(RegExp('generateImprovement'))
      .times(2)
      .reply(200, () => {
        callCount++
        return {
          result: {
            data: {
              json: {
                title: `Generated Title ${callCount}`,
                text: `Generated improvement text ${callCount}`,
              },
            },
          },
        }
      })

    render(
      <TestWrapper>
        <AddImprovementComposer {...defaultProps} />
      </TestWrapper>
    )

    const input = screen.getByPlaceholderText('Provide additional context')
    userEvent.type(input, 'This needs improvement')
    userEvent.click(screen.getByLabelText('Generate improvement'))

    await waitFor(() => {
      expect(screen.getByText('Generated Title 1')).toBeInTheDocument()
    })

    userEvent.clear(input)
    userEvent.type(input, 'New improvement idea')
    const retryButton = screen.getByText('Retry').closest('button')
    expect(retryButton).toBeEnabled()

    userEvent.click(retryButton!)

    await waitFor(() => {
      expect(screen.getByText('Generated Title 2')).toBeInTheDocument()
    })
  })

  test('disables retry button when input is empty', async () => {
    nock('http://localhost')
      .post(RegExp('generateImprovement'))
      .reply(200, {
        result: {
          data: {
            json: {
              title: 'Generated Title',
              text: 'Generated improvement text',
            },
          },
        },
      })

    render(
      <TestWrapper>
        <AddImprovementComposer {...defaultProps} />
      </TestWrapper>
    )

    const input = screen.getByPlaceholderText('Provide additional context')
    userEvent.type(input, 'This needs improvement')
    userEvent.click(screen.getByLabelText('Generate improvement'))

    await waitFor(() => {
      expect(screen.getByText('Generated Title')).toBeInTheDocument()
    })

    const retryButton = screen.getByText('Retry').closest('button')
    expect(retryButton).toBeDisabled()
  })

  test('handles save improvement functionality', async () => {
    const onAddMock = jest.fn()

    nock('http://localhost')
      .post(RegExp('generateImprovement'))
      .reply(200, {
        result: {
          data: {
            json: {
              title: 'Generated Title',
              text: 'Generated improvement text',
            },
          },
        },
      })
      .post(RegExp('createExternalSource'))
      .reply(200, {
        result: {
          data: {
            json: { id: 42 },
          },
        },
      })
      .post(RegExp('patchBeacon'), {
        json: {
          beaconId: 'beacon-456',
          op: 'add',
          path: '/ai/snippetIds',
          value: [42],
        },
      })
      .reply(200, {
        result: {
          data: {
            json: {},
          },
        },
      })

    render(
      <TestWrapper>
        <AddImprovementComposer {...defaultProps} onAdd={onAddMock} />
      </TestWrapper>
    )

    const input = screen.getByPlaceholderText('Provide additional context')
    userEvent.type(input, 'This needs improvement')
    userEvent.click(screen.getByLabelText('Generate improvement'))

    await waitFor(() => {
      expect(screen.getByText('Generated Title')).toBeInTheDocument()
    })

    const saveButton = screen.getByText('Save Improvement').closest('button')
    userEvent.click(saveButton!)

    await waitFor(() => {
      expect(onAddMock).toHaveBeenCalledTimes(1)
      expect(onAddMock).toHaveBeenCalledWith(42)
    })

    expect(defaultProps.onClose).toHaveBeenCalledTimes(1)
    await waitForRequestsToFinish()
  })

  test('shows animated border when loading', async () => {
    nock('http://localhost')
      .post(RegExp('generateImprovement'))
      .delay(100)
      .reply(200, {
        result: {
          data: {
            json: {
              title: 'Generated Title',
              text: 'Generated improvement text',
            },
          },
        },
      })

    const { container } = render(
      <TestWrapper>
        <AddImprovementComposer {...defaultProps} />
      </TestWrapper>
    )

    const input = screen.getByPlaceholderText('Provide additional context')
    userEvent.type(input, 'This needs improvement')
    userEvent.click(screen.getByLabelText('Generate improvement'))

    await waitFor(() => {
      const animatedBorder = container.querySelector('.is-animating')
      expect(animatedBorder).toBeInTheDocument()
    })
  })

  test('does not require conversationId to render', () => {
    render(
      <TestWrapper>
        <AddImprovementComposer {...defaultProps} conversationId={undefined} />
      </TestWrapper>
    )

    expect(
      screen.getByText(
        'Help AI Answers improve future responses on this topic.'
      )
    ).toBeInTheDocument()
  })

  test('focuses input after state changes', async () => {
    nock('http://localhost')
      .post(RegExp('generateImprovement'))
      .reply(200, {
        result: {
          data: {
            json: {
              title: 'Generated Title',
              text: 'Generated improvement text',
            },
          },
        },
      })

    render(
      <TestWrapper>
        <AddImprovementComposer {...defaultProps} />
      </TestWrapper>
    )

    const input = screen.getByPlaceholderText('Provide additional context')
    userEvent.type(input, 'This needs improvement')
    userEvent.click(screen.getByLabelText('Generate improvement'))

    await waitFor(() => {
      expect(screen.getByText('Generated Title')).toBeInTheDocument()
    })

    await waitFor(() => {
      expect(
        screen.getByPlaceholderText('Share additional context')
      ).toHaveFocus()
    })
  })
})
