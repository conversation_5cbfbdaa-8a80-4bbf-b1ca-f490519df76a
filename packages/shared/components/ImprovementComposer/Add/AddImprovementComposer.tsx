import classNames from 'classnames'
import React from 'react'

import { trpc } from '../../../utils/trpc'
import { renderContent } from './utils/renderContent'

import { useAddImprovement } from './hooks/useAddImprovement'
import { useAutoFocusOn } from './hooks/useAutoFocusOn'
import { useComposerState } from './hooks/useComposerState'
import { useImprovementForm } from './hooks/useImprovementForm'
import { useOnEscapeKey } from './hooks/useOnEscapeKey'

import { COMPOSER_STATES } from './constants'

import {
  AnimatedBorderUI,
  ContentContainerUI,
  ImprovementComposerContainerUI,
  ImprovementComposerUI,
  InputContainerUI,
} from './AddImprovementComposer.css'

import { AddImprovementFloatingControls } from './AddImprovementFloatingControls'
import { ComposerInput } from './components/ComposerInput'

export interface AddImprovementComposerProps {
  isWideLayout?: boolean
  onClose: () => void
  onAdd?: (id: number) => void
  onError?: () => void
  className?: string
  conversationId?: number
  beaconId: string
}

export const AddImprovementComposer = ({
  isWideLayout,
  onClose,
  onAdd,
  className,
  conversationId,
  beaconId,
  onError,
}: AddImprovementComposerProps) => {
  const generateImprovement =
    trpc.externalSources.generateImprovement.useMutation()

  const handleSuccess = (id: number) => {
    onAdd?.(id)
    onClose()
  }

  const { add, isAddingImprovement } = useAddImprovement(
    beaconId,
    handleSuccess
  )

  useOnEscapeKey(onClose)

  const state = useComposerState(generateImprovement)

  const inputRef = useAutoFocusOn(
    state === COMPOSER_STATES.INITIAL || state === COMPOSER_STATES.GENERATED
  )

  const { userInput, setUserInput, handleSubmit } = useImprovementForm(
    generateImprovement,
    conversationId
  )

  const handleSave = () => {
    if (!generateImprovement.data) return

    add(
      {
        type: 'snippet',
        text: generateImprovement.data.text,
        name: generateImprovement.data.title,
        convoId: conversationId,
      },
      { onError }
    )
  }

  return (
    <ImprovementComposerUI
      className={classNames(className, { 'is-wide-layout': isWideLayout })}
    >
      <ImprovementComposerContainerUI>
        <AddImprovementFloatingControls handleClose={onClose} />
        <AnimatedBorderUI
          className={state === COMPOSER_STATES.LOADING ? 'is-animating' : ''}
        />
        <ContentContainerUI>
          {renderContent(
            state,
            generateImprovement,
            handleSave,
            isAddingImprovement
          )}
          <InputContainerUI>
            <ComposerInput
              state={state}
              userInput={userInput}
              onInputChange={setUserInput}
              onSubmit={handleSubmit}
              isDisabled={generateImprovement.isLoading || isAddingImprovement}
              inputRef={inputRef}
            />
          </InputContainerUI>
        </ContentContainerUI>
      </ImprovementComposerContainerUI>
    </ImprovementComposerUI>
  )
}
