import React from 'react'

import { capitalize } from 'shared/utils/StringUtils'

import { BlankSlate, CheckmarkCardGridUI } from './UserSelector.css'

import CheckmarkCard from 'hsds/components/checkmark-card'
import { MailboxUser } from 'shared/types/global'

export function toStartCase(toConvert: string) {
  return toConvert?.split(' ').map(capitalize).join(' ')
}

interface UserSelectorProps {
  setUsers(users: number | number[]): void
  users: MailboxUser[]
  value: number[]
}

function UserSelector({ setUsers, users = [], value }: UserSelectorProps) {
  if (!users || users.length === 0) {
    return <BlankSlate>No one matches your search.</BlankSlate>
  }

  const selectedUsers = value.map(Number)

  return (
    <CheckmarkCardGridUI
      value={selectedUsers}
      choiceMaxWidth="170px"
      choiceHeight="160px"
      onChange={setUsers}
    >
      {users.map(user => {
        return (
          <CheckmarkCard
            avatar={user.photoUrl || ''}
            label={user.fullName}
            key={user.id}
            value={user.id}
            aria-label={`${user.fullName} ${user.role}`}
            aria-checked={!!selectedUsers.find(userId => userId === user.id)}
            subtitle={toStartCase(user.role || '')}
            title={user.fullName}
          />
        )
      })}
    </CheckmarkCardGridUI>
  )
}

export default UserSelector
