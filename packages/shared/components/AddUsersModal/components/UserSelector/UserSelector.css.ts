import styled from 'styled-components'

import { getColor } from 'hsds/utils/color'

import CheckmarkCard from 'hsds/components/checkmark-card'

export const BlankSlate = styled('div')`
  align-items: center;
  color: ${getColor('charcoal.600')};
  display: flex;
  font-size: 14px;
  height: 100%;
  justify-content: center;
`

export const CheckmarkCardGridUI = styled(CheckmarkCard.Grid)`
  grid-template-columns: repeat(4, 170px);
  margin-top: 20px;

  .c-CheckmarkCard {
    cursor: pointer;
  }
`
