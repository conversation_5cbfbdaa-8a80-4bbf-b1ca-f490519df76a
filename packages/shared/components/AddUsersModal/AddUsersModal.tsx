import { useEffect, useState } from 'react'

import useFilteredUsers from './hooks/useFilteredUsers'
import { Portal } from 'hsds/hooks'
import { useHotkeysContext } from 'shared/hooks'

import {
  ModalFooterUI,
  ModalTitleTextUI,
  ModalTitleUI,
  ModalUI,
} from './AddUsersModal.css'

import UserSelector from './components/UserSelector/UserSelector'
import Button from 'hsds/components/button'
import Icon from 'hsds/components/icon'
import Input from 'hsds/components/input'
import { HeaderAndFooter } from 'hsds/components/simple-modal'
import Tooltip from 'hsds/components/tooltip'
import SearchMedium from 'hsds/icons/search'
import ConditionalWrapper from 'shared/components/ConditionalWrapper'
import { MailboxUser } from 'shared/types/global'

interface AddUserModalProps {
  /** Callback function called when users are confirmed/added. Receives array of selected user IDs */
  onConfirm: (selectedUsers: number[]) => void
  /** Callback function called when the modal is closed/cancelled */
  onClose: () => void
  /** Controls whether the modal is visible */
  isOpen: boolean
  /** Title text displayed at the top of the modal */
  title: string
  /** Text displayed on the primary action button (e.g., "Add Users", "Save") */
  primaryButtonText: string
  /** Array of users to display in the selection grid */
  users: MailboxUser[]
  /** Array of user IDs that should be pre-selected when modal opens */
  existingUsers?: number[]
  /** Maximum number of users that can be selected. Shows tooltip warning when exceeded */
  maxAssignees?: number
  /** Optional hotkey scope to disable while modal is open (prevents conflicts with background shortcuts) */
  hotkeyScopeToDisable?: string
}

function AddUsersModal({
  users = [],
  onConfirm = () => {},
  onClose = () => {},
  isOpen = false,
  title = '',
  existingUsers = [],
  primaryButtonText,
  maxAssignees = 15,
  hotkeyScopeToDisable,
}: AddUserModalProps) {
  const [selectedUsers, setNewUsers] = useState<number[]>(existingUsers)
  const { searchBarProps, filteredItems } = useFilteredUsers(users, 'fullName')

  useEffect(() => {
    if (isOpen) {
      setNewUsers(existingUsers)
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isOpen])

  const { enableScopes, disableScopes } = useHotkeysContext()

  // Ignore side panel keyboard shortcuts while the modal is open
  useEffect(() => {
    if (hotkeyScopeToDisable) {
      if (isOpen) {
        disableScopes([hotkeyScopeToDisable])
      } else {
        enableScopes([hotkeyScopeToDisable])
      }
      return () => enableScopes([hotkeyScopeToDisable])
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isOpen, hotkeyScopeToDisable])

  const handleAddUsers = () => {
    onConfirm(selectedUsers)
  }

  const handleSelectUsers = (newUsers: number | number[]) => {
    const newValue = Array.isArray(newUsers) ? newUsers : [newUsers]
    setNewUsers(newValue)
  }

  const handleCloseModal = () => {
    setNewUsers([])
    onClose()
  }

  const modalTitle = (
    <ModalTitleUI>
      <ModalTitleTextUI>{title}</ModalTitleTextUI>
      <Input
        {...searchBarProps}
        autocomplete="off"
        inlineSuffix={<Icon icon={SearchMedium} size="24" />}
        name="quick-search"
        placeholder="Quick Search"
        tabindex="0"
        width="286px"
        aria-label="Search Users"
      />
    </ModalTitleUI>
  )

  const modalFooter = (
    <ModalFooterUI>
      <Button onClick={handleCloseModal} linked color="grey">
        Cancel
      </Button>
      <ConditionalWrapper
        condition={selectedUsers.length > maxAssignees}
        wrapper={children => (
          <Tooltip
            title={`Select up to ${maxAssignees} assignees`}
            withTriggerWrapper
          >
            {children}
          </Tooltip>
        )}
      >
        <Button
          size="lg"
          color="blue"
          disabled={
            !selectedUsers.length || selectedUsers?.length > maxAssignees
          }
          onClick={handleAddUsers}
        >
          {primaryButtonText}
        </Button>
      </ConditionalWrapper>
    </ModalFooterUI>
  )

  return (
    <Portal>
      <ModalUI
        show={isOpen}
        width="890px"
        height="770px"
        withCloseButton={false}
        aria-label={title}
        closeOnClickOutside="modal"
        trapFocus
        onClose={handleCloseModal}
      >
        <HeaderAndFooter header={modalTitle} footer={modalFooter}>
          <UserSelector
            setUsers={handleSelectUsers}
            users={filteredItems}
            value={selectedUsers}
          />
        </HeaderAndFooter>
      </ModalUI>
    </Portal>
  )
}

export default AddUsersModal
