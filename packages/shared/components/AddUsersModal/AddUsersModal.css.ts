import styled from 'styled-components'

import { getColor } from 'hsds/utils/color'

import SimpleModal from 'hsds/components/simple-modal'
import Text from 'hsds/components/text'

export const ModalTitleUI = styled('div')`
  align-items: center;
  display: flex;
  height: 100%;
`

export const ModalFooterUI = styled('div')`
  align-items: center;
  justify-content: space-between;
  display: flex;
  height: 100%;
`

export const ModalUI = styled(SimpleModal)`
  z-index: 9999;
  @media (max-width: 768px) {
    align-items: flex-start;
    overflow-x: auto;
  }

  @media (max-width: 888px) {
    .SimpleModal__body {
      padding-left: 30px;
      padding-right: 30px;
    }
  }

  .SimpleModal {
    max-width: 100%;
    width: 890px;
    min-width: 768px;
    min-height: 300px;
    margin: 40px 0;
  }
`

export const ModalTitleTextUI = styled('h1')`
  flex: 1;
`

export const UserCountTextUI = styled(Text)`
  color: ${getColor('charcoal.900')} !important;
`
