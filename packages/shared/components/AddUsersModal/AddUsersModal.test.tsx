import { fireEvent, render, screen, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'

import AddUsersModal from './AddUsersModal'
import { MailboxUser } from 'shared/types/global'

const MAX_ASSIGNEES = 15

const users: MailboxUser[] = [
  {
    id: 1,
    fullName: 'User One',
    photoUrl: 'user_one.jpg',
    role: 'admin',
    initials: 'UO',
    firstName: 'User',
    lastName: 'One',
    mentionName: 'user.one',
    email: '<EMAIL>',
    type: '3',
  },
  {
    id: 2,
    fullName: 'User Two',
    photoUrl: 'user_two.jpg',
    role: 'user',
    initials: 'UT',
    firstName: 'User',
    lastName: 'Two',
    mentionName: 'user.two',
    email: '<EMAIL>',
    type: '3',
  },
]

const mockProps = {
  isOpen: true,
  title: 'Add Users',
  users: users,
  onClose: jest.fn(),
  onConfirm: jest.fn(),
  primaryButtonText: 'Add Users',
  maxAssignees: MAX_ASSIGNEES,
}

describe('AddUsersModal', () => {
  it('should display users in the modal', () => {
    render(<AddUsersModal {...mockProps} />)

    expect(screen.getByLabelText('User One admin')).toBeVisible()
    expect(screen.getByLabelText('User Two user')).toBeVisible()
  })
  it('should show empty body when no users', () => {
    render(<AddUsersModal {...mockProps} users={[]} />)

    expect(screen.getByText('No one matches your search.')).toBeInTheDocument()
  })
  it('should have confirm button disabled when no users selected', () => {
    render(<AddUsersModal {...mockProps} />)

    expect(screen.getByRole('button', { name: 'Add Users' })).toBeDisabled()
  })
  it('should be able to search for users', async () => {
    render(<AddUsersModal {...mockProps} />)

    fireEvent.change(screen.getByPlaceholderText('Quick Search'), {
      target: { value: 'User One' },
    })

    await waitFor(() => {
      expect(screen.getByLabelText('User One admin')).toBeVisible()
      expect(screen.queryByLabelText('User Two user')).not.toBeInTheDocument()
    })
  })

  it('should be able to add users once selected', async () => {
    render(<AddUsersModal {...mockProps} />)

    userEvent.click(screen.getByLabelText('User One admin'))
    userEvent.click(screen.getByLabelText('User Two user'))

    userEvent.click(screen.getByRole('button', { name: 'Add Users' }))

    await waitFor(() => {
      expect(mockProps.onConfirm).toHaveBeenCalledWith([1, 2])
    })
  })

  it('Should be able to select cancel to close the modal', async () => {
    render(<AddUsersModal {...mockProps} />)

    userEvent.click(screen.getByText('Cancel'))

    await waitFor(() => {
      expect(mockProps.onClose).toHaveBeenCalled()
    })
  })

  it('should un-select selected users on cancel', () => {
    render(<AddUsersModal {...mockProps} />)

    userEvent.click(
      screen.getByRole('checkbox', {
        name: /user two user/i,
      })
    )

    expect(
      screen.getByRole('checkbox', {
        name: /user two user/i,
      })
    ).toBeChecked()

    userEvent.click(screen.getByText('Cancel'))

    expect(
      screen.getByRole('checkbox', {
        name: /user two user/i,
      })
    ).not.toBeChecked()
  })

  it(`should not be able to select more than ${MAX_ASSIGNEES} users`, async () => {
    const users: MailboxUser[] = []
    for (let i = 1; i <= MAX_ASSIGNEES + 1; i++) {
      users.push({
        id: i,
        fullName: `User ${i}`,
        photoUrl: `user_${i}.jpg`,
        role: 'admin',
        initials: `U${i}`,
        firstName: 'User',
        lastName: `${i}`,
        mentionName: `user.${i}`,
        email: `user.${i}@test.com`,
        type: '3',
      })
    }

    render(<AddUsersModal {...mockProps} users={users} />)

    for (let i = 1; i <= MAX_ASSIGNEES + 1; i++) {
      userEvent.click(
        screen.getByRole('checkbox', {
          name: `User ${i} admin`,
        })
      )
    }

    expect(screen.getByRole('button', { name: 'Add Users' })).toBeDisabled()

    // Deselect an user.
    userEvent.click(
      screen.getByRole('checkbox', {
        name: `User ${MAX_ASSIGNEES + 1} admin`,
      })
    )

    expect(screen.getByRole('button', { name: 'Add Users' })).toBeEnabled()

    userEvent.click(screen.getByRole('button', { name: 'Add Users' }))

    const expectedFilterValue = Array.from(
      { length: MAX_ASSIGNEES },
      (_, i) => i + 1
    )

    await waitFor(() => {
      expect(mockProps.onConfirm).toHaveBeenCalledWith(expectedFilterValue)
    })
  })
})
