import { useEffect, useState } from 'react'

import { MailboxUser } from 'shared/types/global'

function useFilteredUsers(
  allItems: MailboxUser[],
  attribute: keyof MailboxUser
) {
  const [query, setQuery] = useState('')
  const [filteredItems, setFilteredItems] = useState(allItems)

  useEffect(() => {
    if (!allItems.length) {
      return
    }

    const cleanQuery = query.toLowerCase().trim()

    setFilteredItems(
      allItems.reduce((acc, curr) => {
        const item = curr[attribute]
        if (
          typeof item === 'string' &&
          item.toLowerCase().indexOf(cleanQuery) !== -1
        ) {
          acc.push(curr)
        }

        return acc
      }, [] as MailboxUser[])
    )
  }, [query, allItems, attribute])

  return {
    searchBarProps: {
      onChange: setQuery,
      value: query,
    },
    filteredItems,
  }
}

export default useFilteredUsers
