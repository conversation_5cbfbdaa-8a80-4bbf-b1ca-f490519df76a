import { act, renderHook } from '@testing-library/react-hooks'

import { fetchMessages } from '../../../utils/fetchMessages'
import {
  getHsAppLocalStorageValue,
  setHsAppLocalStorageValue,
} from 'shared/utils'
import { useWelcomeModal } from 'shared/utils/welcomeModal/welcomeModalUtils'

import {
  COMPLETED_TOURS_KEY,
  UNSEEN_TOURS_KEY,
  getUnseenTours,
  useToursState,
} from './useToursState'

jest.mock('../../../utils/fetchMessages')
jest.mock('shared/utils/welcomeModal/welcomeModalUtils')

const mockFetchMessages = fetchMessages as jest.MockedFunction<
  typeof fetchMessages
>

const mockUseWelcomeModal = useWelcomeModal as jest.MockedFunction<
  typeof useWelcomeModal
>

describe('useToursState', () => {
  const mockTourData = [
    { id: 'tour1', messageId: 'msg1', steps: [] },
    { id: 'tour2', messageId: 'msg2', steps: [] },
  ]

  const mockTourGroupId = 'test-tours'

  beforeEach(() => {
    jest.clearAllMocks()
    setHsAppLocalStorageValue(UNSEEN_TOURS_KEY, [])
    setHsAppLocalStorageValue(COMPLETED_TOURS_KEY, [])
    setHsAppLocalStorageValue('active-tour-group', null)

    // Mock useWelcomeModal to return dismissed by default
    mockUseWelcomeModal.mockReturnValue({
      isWelcomeModalDismissed: true,
      showWelcomeModal: false,
      handleClose: jest.fn(),
    })
  })

  it('should fetch and set unseen tours on mount', async () => {
    mockFetchMessages.mockResolvedValueOnce({
      messages: [{ id: 'msg1' }, { id: 'msg2' }],
    })

    const { result, waitForNextUpdate } = renderHook(() =>
      useToursState({ data: mockTourData, tourGroupId: mockTourGroupId })
    )

    expect(result.current.nextTour).toBeUndefined()
    expect(result.current.isTourGroupActive).toBe(true)

    expect(mockFetchMessages).toHaveBeenCalledTimes(1)

    await waitForNextUpdate()

    expect(result.current.nextTour).toBe(mockTourData[0])
  })

  it('should respect the unseen tours state in local storage', async () => {
    setHsAppLocalStorageValue(UNSEEN_TOURS_KEY, [''])
    setHsAppLocalStorageValue(COMPLETED_TOURS_KEY, ['tour1'])

    mockFetchMessages.mockResolvedValueOnce({
      messages: [{ id: 'msg1' }, { id: 'msg2' }],
    })

    const { result, waitForNextUpdate } = renderHook(() =>
      useToursState({ data: mockTourData, tourGroupId: mockTourGroupId })
    )

    expect(result.current.nextTour).toBeUndefined()
    expect(result.current.isTourGroupActive).toBe(true)

    expect(mockFetchMessages).toHaveBeenCalledTimes(1)

    await waitForNextUpdate()

    expect(result.current.nextTour).toBe(mockTourData[1])
  })

  it('should respect extraConditions when finding next tour', async () => {
    mockFetchMessages.mockResolvedValueOnce({
      messages: [{ id: 'msg1' }, { id: 'msg2' }],
    })

    const extraConditions = {
      tour1: false,
      tour2: true,
    }

    const { result, waitForNextUpdate } = renderHook(() =>
      useToursState({
        data: mockTourData,
        extraConditions,
        tourGroupId: mockTourGroupId,
      })
    )

    expect(result.current.nextTour).toBeUndefined()

    await waitForNextUpdate()

    // Should skip tour1 since its condition is false
    expect(result.current.nextTour).toBe(mockTourData[1])
  })

  it('should not show any tours when all extraConditions are false', async () => {
    mockFetchMessages.mockResolvedValueOnce({
      messages: [{ id: 'msg1' }, { id: 'msg2' }],
    })

    const extraConditions = {
      tour1: false,
      tour2: false,
    }

    const { result, waitForNextUpdate } = renderHook(() =>
      useToursState({
        data: mockTourData,
        extraConditions,
        tourGroupId: mockTourGroupId,
      })
    )

    expect(result.current.nextTour).toBeUndefined()
    expect(result.current.isTourGroupActive).toBe(true)

    await waitForNextUpdate()

    expect(result.current.nextTour).toBeUndefined()
  })

  it('should show tour when no extraCondition is specified for it', async () => {
    mockFetchMessages.mockResolvedValueOnce({
      messages: [{ id: 'msg1' }, { id: 'msg2' }],
    })

    const extraConditions = {
      tour2: false,
    }

    const { result, waitForNextUpdate } = renderHook(() =>
      useToursState({
        data: mockTourData,
        extraConditions,
        tourGroupId: mockTourGroupId,
      })
    )

    expect(result.current.nextTour).toBeUndefined()
    expect(result.current.isTourGroupActive).toBe(true)

    await waitForNextUpdate()

    // Should show tour1 since it has no condition specified
    expect(result.current.nextTour).toBe(mockTourData[0])
  })

  it('should not be active when another tour group is active', () => {
    setHsAppLocalStorageValue('active-tour-group', 'other-tours')

    const { result } = renderHook(() =>
      useToursState({ data: mockTourData, tourGroupId: mockTourGroupId })
    )

    expect(result.current.isTourGroupActive).toBe(false)
  })

  it('should reset active-tour-group when all tours are completed', async () => {
    mockFetchMessages.mockResolvedValueOnce({
      messages: [{ id: 'msg1' }, { id: 'msg2' }],
    })

    const { result, waitForNextUpdate } = renderHook(() =>
      useToursState({ data: mockTourData, tourGroupId: mockTourGroupId })
    )

    await waitForNextUpdate()

    // Verify initial state
    expect(result.current.nextTour).toBe(mockTourData[0])
    expect(getHsAppLocalStorageValue('active-tour-group')).toBe(mockTourGroupId)

    // Complete first tour
    act(() => {
      result.current.handleTourComplete('tour1')
    })

    // Verify state after first tour
    expect(result.current.nextTour).toBe(mockTourData[1])
    expect(getHsAppLocalStorageValue('active-tour-group')).toBe(mockTourGroupId)

    // Complete second tour
    act(() => {
      result.current.handleTourComplete('tour2')
    })

    // Verify final state
    expect(result.current.nextTour).toBeUndefined()
    expect(getHsAppLocalStorageValue('active-tour-group')).toBeNull()
  })

  it('should not fetch tours when welcome modal is not dismissed', () => {
    mockUseWelcomeModal.mockReturnValue({
      isWelcomeModalDismissed: false,
      showWelcomeModal: false,
      handleClose: jest.fn(),
    })

    const { result } = renderHook(() =>
      useToursState({ data: mockTourData, tourGroupId: mockTourGroupId })
    )

    expect(result.current.isTourGroupActive).toBe(false)
    expect(result.current.nextTour).toBeUndefined()
    expect(mockFetchMessages).not.toHaveBeenCalled()
  })
})

describe('getUnseenTours', () => {
  it('should return correct unseen tour IDs', () => {
    const tours = [
      { id: 'tour1', messageId: 'msg1', steps: [] },
      { id: 'tour2', messageId: 'msg2', steps: [] },
      { id: 'tour3', messageId: 'msg3', steps: [] },
    ]

    const messages = [{ id: 'msg1' }, { id: 'msg3' }, { id: 'other-message' }]

    const result = getUnseenTours(tours, messages)
    expect(result).toEqual(['tour1', 'tour3'])
  })

  it('should return empty array when no messages match', () => {
    const tours = [
      { id: 'tour1', messageId: 'msg1', steps: [] },
      { id: 'tour2', messageId: 'msg2', steps: [] },
    ]

    const messages = [{ id: 'other-message1' }, { id: 'other-message2' }]

    const result = getUnseenTours(tours, messages)
    expect(result).toEqual([])
  })
})
