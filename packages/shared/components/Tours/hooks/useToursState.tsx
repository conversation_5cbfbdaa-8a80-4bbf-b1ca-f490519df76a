import { useEffect, useRef, useState } from 'react'

import { fetchMessages } from '../../../utils/fetchMessages'
import { getHsAppLocalStorageValue } from 'shared/utils'
import { setHsAppLocalStorageValue } from 'shared/utils'
import { useWelcomeModal } from 'shared/utils/welcomeModal/welcomeModalUtils'

import { usePersistedState } from 'shared/hooks'

import { ExtraConditions, TourData } from '../types'

export const UNSEEN_TOURS_KEY = 'unseen-tours'
export const COMPLETED_TOURS_KEY = 'completed-tours'

// Get the list of tour IDs that the user hasn't seen yet,
// based on the list of all tours and the list of all messages we get from the API.
export function getUnseenTours(
  allTours: TourData[],
  allMessages: { id: string }[]
) {
  const allTourMessageIds = allTours.map(tour => tour.messageId)
  const tourMessagesIds = allMessages
    .filter(message => allTourMessageIds.includes(message.id))
    .map(message => message.id)

  return allTours
    .filter(tour => tourMessagesIds.includes(tour.messageId))
    .map(tour => tour.id)
}

export function useToursState({
  data,
  extraConditions,
  tourGroupId,
}: {
  data: TourData[]
  extraConditions?: ExtraConditions
  tourGroupId: string
}) {
  const requestCountRef = useRef(0)
  const [isTourGroupActive, setIsTourGroupActive] = useState(false)

  const [unseenTours, setUnseenTours] = usePersistedState(
    UNSEEN_TOURS_KEY,
    []
  ) as [string[], (value: string[]) => void]

  const [completedTours, setCompletedTours] = usePersistedState(
    COMPLETED_TOURS_KEY,
    []
  ) as [string[], (value: string[]) => void]

  const { isWelcomeModalDismissed } = useWelcomeModal()

  const findNextAvailableTour = () => {
    return data.find(tour => {
      let matchesExtraConditions = true

      if (extraConditions && extraConditions[tour.id] !== undefined) {
        matchesExtraConditions = extraConditions[tour.id]
      }

      return (
        unseenTours.includes(tour.id) &&
        !completedTours.includes(tour.id) &&
        matchesExtraConditions
      )
    })
  }

  const handleTourComplete = (tourId: string) => {
    const unseenToursAfterCompletion = unseenTours.filter(id => id !== tourId)
    setUnseenTours(unseenToursAfterCompletion)

    setCompletedTours([...completedTours, tourId])

    // if it's the last tour of the tour group, reset the active-tour-group in local storage
    if (unseenToursAfterCompletion.length === 0) {
      setHsAppLocalStorageValue('active-tour-group', null)
    }
  }

  // if we don't have an active-tour-group in local storage, set it to the current tourGroupId
  useEffect(() => {
    const activeTourGroupId = getHsAppLocalStorageValue('active-tour-group')

    // if it doesn't exists or if it's null, set it to the current tourGroupId
    if (!activeTourGroupId) {
      setHsAppLocalStorageValue('active-tour-group', tourGroupId)
    }

    const currentActiveTourGroupId =
      getHsAppLocalStorageValue('active-tour-group')

    if (currentActiveTourGroupId === tourGroupId && isWelcomeModalDismissed) {
      setIsTourGroupActive(true)

      const getUnseenToursFromMessagesAPI = async () => {
        // Prevent multiple requests from being made if this effect is called multiple times.
        // This isn't strictly necessary, but we ran into an issue with an infinite loop
        // in the past, so we're being extra cautious here. See INB-979 for more details.
        if (requestCountRef.current > 0) return
        try {
          requestCountRef.current += 1
          const result = await fetchMessages()
          if (!result || !result.messages) return
          setUnseenTours(getUnseenTours(data, result.messages))
        } catch (error) {
          console.error('Error fetching messages:', error)
        }
      }
      void getUnseenToursFromMessagesAPI()
    }
  }, [tourGroupId, setUnseenTours, data, isWelcomeModalDismissed])

  const nextTour = findNextAvailableTour()

  return {
    nextTour,
    handleTourComplete,
    isTourGroupActive,
  }
}
