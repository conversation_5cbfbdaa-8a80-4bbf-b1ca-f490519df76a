import { fireEvent, render, screen } from '@testing-library/react'

import { usePersistedState } from 'shared/hooks'

import Tours from './Tours'
import { TourData } from './types'

const UNSEEN_TOURS_KEY = 'unseen-tours'
const COMPLETED_TOURS_KEY = 'completed-tours'

jest.mock('shared/hooks', () => ({
  usePersistedState: jest.fn(() => [[], jest.fn()]),
}))

jest.mock('shared/utils/welcomeModal/welcomeModalUtils', () => ({
  useWelcomeModal: jest.fn(() => ({
    isWelcomeModalDismissed: true,
  })),
}))

jest.mock('./components/Tour/Tour', () => ({
  __esModule: true,
  default: ({
    tour,
    onComplete,
  }: {
    tour: TourData
    onComplete: (id: string) => void
  }) => (
    <div data-testid="mock-tour">
      <span>{tour.id}</span>
      <button onClick={() => onComplete(tour.id)}>Complete</button>
    </div>
  ),
}))

const mockTourData: TourData[] = [
  { id: 'tour1', messageId: '', steps: [] },
  { id: 'tour2', messageId: '', steps: [] },
  { id: 'tour3', messageId: '', steps: [] },
]

describe('Tours', () => {
  it('should not render a tour by default', () => {
    render(<Tours data={mockTourData} tourGroupId="test-tours" />)
    expect(screen.queryByTestId('mock-tour')).not.toBeInTheDocument()
  })

  it('should not render a tour if the unseen tours state is empty', () => {
    ;(usePersistedState as jest.Mock).mockImplementation(() => {
      return [[], jest.fn()]
    })

    render(<Tours data={mockTourData} tourGroupId="test-tours" />)
    expect(screen.queryByTestId('mock-tour')).not.toBeInTheDocument()
  })

  it('should render the first unseen tour if none are completed', () => {
    ;(usePersistedState as jest.Mock).mockImplementation(key => {
      if (key === UNSEEN_TOURS_KEY) {
        return [['tour1', 'tour2', 'tour3'], jest.fn()]
      }
      return [[], jest.fn()]
    })

    render(<Tours data={mockTourData} tourGroupId="test-tours" />)
    expect(screen.getByTestId('mock-tour')).toBeInTheDocument()
    expect(screen.getByText('tour1')).toBeInTheDocument()
  })

  it('should render the first unseen tour if none are completed', () => {
    ;(usePersistedState as jest.Mock).mockImplementation(key => {
      if (key === UNSEEN_TOURS_KEY) {
        return [['tour1', 'tour2', 'tour3'], jest.fn()]
      }
      if (key === COMPLETED_TOURS_KEY) {
        return [['tour1'], jest.fn()]
      }
      return [[], jest.fn()]
    })

    render(<Tours data={mockTourData} tourGroupId="test-tours" />)
    expect(screen.getByTestId('mock-tour')).toBeInTheDocument()
    expect(screen.getByText('tour2')).toBeInTheDocument()
  })

  it('should not render a tour if all are completed', () => {
    ;(usePersistedState as jest.Mock).mockImplementation(key => {
      if (key === UNSEEN_TOURS_KEY) {
        return [['tour1', 'tour2', 'tour3'], jest.fn()]
      }
      if (key === COMPLETED_TOURS_KEY) {
        return [['tour1', 'tour2', 'tour3'], jest.fn()]
      }
    })

    render(<Tours data={mockTourData} tourGroupId="test-tours" />)
    expect(screen.queryByTestId('mock-tour')).not.toBeInTheDocument()
  })

  it('moves to the next tour when current tour is completed', () => {
    const setUnseenTours = jest.fn()
    const setCompletedTours = jest.fn()
    ;(usePersistedState as jest.Mock).mockImplementation(key => {
      if (key === UNSEEN_TOURS_KEY) {
        return [['tour2', 'tour3'], setUnseenTours]
      }
      if (key === COMPLETED_TOURS_KEY) {
        return [['tour1'], setCompletedTours]
      }
    })

    render(<Tours data={mockTourData} tourGroupId="test-tours" />)

    // Complete the second tour
    fireEvent.click(screen.getByText('Complete'))

    expect(setCompletedTours).toHaveBeenCalledWith(['tour1', 'tour2'])
    expect(setUnseenTours).toHaveBeenCalledWith(['tour3'])
  })
})
