import styled from 'styled-components'

import ButtonIcon from 'hsds/components/button-icon'
import { getToken } from 'hsds/tokens'

export const FloatingToolbarUI = styled.div`
  --hsds-floating-toolbar-padding-left: 8px;
  --hsds-floating-toolbar-padding-top: 0px;
  --hsds-floating-toolbar-height: 48px;
  --hsds-floating-toolbar-background-color: transparent;
  --hsds-floating-toolbar-divider-color: transparent;
  --hsds-floating-toolbar-z-index: 99999;
  --hsds-floating-toolbar-border-radius: 8px;

  display: inline-flex;
  align-items: center;
  justify-content: center;

  flex-flow: row nowrap;
  height: var(--hsds-floating-toolbar-height);
  padding: var(--hsds-floating-toolbar-padding-top)
    var(--hsds-floating-toolbar-padding-left);
  position: absolute;
  left: 0;
  top: 0;
  border-radius: var(--hsds-floating-toolbar-border-radius);
  opacity: 0;
  background: var(--hsds-floating-toolbar-background-color);
  pointer-events: none;
  z-index: var(--hsds-floating-toolbar-z-index);

  &.is-color-charcoal {
    --hsds-floating-toolbar-background-color: ${getToken(
      'buttonicon.color.charcoal.main'
    )};
    --hsds-floating-toolbar-divider-color: ${getToken(
      'floatingToolbar.color.divider.charcoal'
    )};
  }
  &.is-color-clay {
    --hsds-floating-toolbar-background-color: ${getToken(
      'buttonicon.color.clay.main'
    )};
    --hsds-floating-toolbar-divider-color: ${getToken(
      'floatingToolbar.color.divider.clay'
    )};
  }

  &.is-visible {
    pointer-events: auto;
    opacity: 1;
  }
`

export const FloatingToolbarBodyUI = styled.div`
  display: inline-flex;
  align-items: center;
`

export const FloatingToolbarDividerUI = styled.span`
  display: inline-flex;
  width: 1px;
  height: 24px;
  margin-right: 5px;
  margin-left: 5px;
  background: var(--hsds-floating-toolbar-divider-color);
`

export const FloatingToolbarButtonUI = styled(ButtonIcon)`
  --hsds-focus-ring-offset: 0px;

  &.is-color-charcoal {
    --hsds-focus-ring-color: ${getToken(
      'color.focusRing.default.innerColorOverDark'
    )};
    --hsds-focus-ring-outer-color: ${getToken(
      'color.focusRing.default.outerColorOverDark'
    )};
  }

  margin: 0;

  &.is-active {
    background-color: var(--hsds-button-hover-background-color);
    border-color: var(--hsds-button-hover-background-color);
    border-color: var(--hsds-button-background-color);
    color: var(--hsds-button-hover-color);
  }

  &.is-disabled {
    --hsds-button-disabled-main-color: var(--hsds-button-background-color);
    --hsds-button-disabled-color: var(--hsds-button-color);
    --hsds-button-disabled-background-color: var(
      --hsds-button-background-color
    );
    --hsds-button-disabled-hover-background-color: var(
      --hsds-button-background-color
    );
    --hsds-button-disabled-border-color: var(--hsds-button-background-color);
    --hsds-button-disabled-hover-border-color: var(
      --hsds-button-background-color
    );
    opacity: 0.5;
  }

  & + & {
    margin-left: 0px;
  }
`
