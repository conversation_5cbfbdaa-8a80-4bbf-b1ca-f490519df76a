import React, { forwardRef, HTMLAttributes, useCallback } from 'react'

import classNames from 'classnames'

import { ButtonIconProps } from 'hsds/components/button-icon'
import Tooltip from 'hsds/components/tooltip'

import { FloatingToolbarButtonUI } from './FloatingToolbar.styles'

import { useFloatingToolbarContext } from './FloatingToolbar'

type PropsWithDefaultOnClick = Omit<ButtonIconProps, 'onClick'> &
  Pick<HTMLAttributes<HTMLButtonElement>, 'onClick'>

export type FloatingToolbarButtonProps = {
  /** Data attr for RTL */
  'data-testid'?: string
  /** Boolean to turn on/off the tooltip */
  shouldShowTooltip?: boolean
  /** Button active state  */
  isActive?: boolean
  /** Button Keyboard shortcut that will be render within the tooltip  */
  tooltipHotkey?: string
  /** Text that will be render within the tooltip  */
  tooltipTitle?: string
} & PropsWithDefaultOnClick

const WrappedFloatingToolbarButton = forwardRef<
  HTMLButtonElement,
  FloatingToolbarButtonProps
>(function FloatingToolbarButton(
  {
    className,
    'data-cy': dataCy = 'FloatingToolbar.Button',
    'data-testid': dataTestId = 'FloatingToolbar.Button',
    icon,
    isActive,
    shouldShowTooltip = true,
    tooltipHotkey,
    tooltipTitle,
    onClick,
    tabIndex,
    ...rest
  },
  ref
) {
  const { color } = useFloatingToolbarContext()

  const buttonClassname = classNames(className, isActive && 'is-active')

  const handleOnClick = useCallback(
    (e: React.MouseEvent<HTMLButtonElement>) => {
      e.preventDefault()
      e.stopPropagation()
      onClick?.(e)
    },
    [onClick]
  )

  const buttonProps = {
    className: buttonClassname,
    ref,
    onClick: handleOnClick,
    color: color || 'charcoal',
    ...rest,
  }

  if (rest['aria-haspopup']) {
    buttonProps['aria-expanded'] = isActive
  }

  const buttonComponent = (
    <FloatingToolbarButtonUI
      data-cy={dataCy}
      data-testid={dataTestId}
      size="md"
      icon={icon}
      tabIndex={tabIndex}
      {...buttonProps}
    />
  )

  if (tooltipTitle && shouldShowTooltip) {
    return (
      <Tooltip
        title={tooltipTitle}
        offset={12}
        badge={tooltipHotkey}
        triggerOn="hover focus"
        tabIndex={tabIndex}
      >
        {buttonComponent}
      </Tooltip>
    )
  }

  return buttonComponent
})

export default WrappedFloatingToolbarButton
