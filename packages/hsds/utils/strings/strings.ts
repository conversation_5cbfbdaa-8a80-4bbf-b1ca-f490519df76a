import DOMPurify from 'dompurify'

/**
 * Checks if a string is a valid URL
 * @param {string} url - The string to validate as URL
 * @returns {boolean} - Returns true if the string is a valid URL, false otherwise
 */
export const isValidUrl = (url: string): boolean => {
  if (!url) return false

  // regex check for common syntax errors like "http:/example.com"
  const validUrlPattern = /^https?:\/\/[^/]/i
  if (!validUrlPattern.test(url)) {
    return false
  }

  try {
    const urlObj = new URL(url)
    const hasValidProtocol =
      urlObj.protocol === 'http:' || urlObj.protocol === 'https:'
    const hasValidHost = !!urlObj.host && urlObj.host.includes('.')
    const hasInvalidPattern =
      urlObj.hostname.includes('..') ||
      urlObj.hostname.startsWith('.') ||
      urlObj.hostname.endsWith('.')

    return hasValidProtocol && hasValidHost && !hasInvalidPattern
  } catch (error) {
    // If the URL is invalid, URL constructor will throw an error
    return false
  }
}

export const normalizeUrl = (str?: string) => {
  if (str === undefined) {
    return ''
  }

  if (str.search(/^http[s]?:\/\//) === -1) {
    return `http://${str}`
  }

  return str
}

export const newlineToHTML = (str?: string) => {
  if (str === undefined) {
    return ''
  }

  return str.trim().replace(/\r?\n/g, '<br>')
}

// Taken from the React escapeTextForBrowser internal utility
const escapeHtmlRegExp = /["'&<>]/

/**
 * Escape HTML special characters in the string for output in the browser.
 *
 * @param {string} str
 * @returns {string}
 */
export const escapeHTML = (str?: string) => {
  if (str === undefined) {
    return ''
  }

  const match = escapeHtmlRegExp.exec(str)

  if (!match) {
    return str
  }

  let escape
  let html = ''
  let index
  let lastIndex = 0

  for (index = match.index; index < str.length; index++) {
    switch (str.charCodeAt(index)) {
      case 34: // "
        escape = '&quot;'
        break
      case 38: // &
        escape = '&amp;'
        break
      case 39: // '
        escape = '&#x27;'
        break
      case 60: // <
        escape = '&lt;'
        break
      case 62: // >
        escape = '&gt;'
        break
      default:
        continue
    }

    if (lastIndex !== index) {
      html += str.substring(lastIndex, index)
    }

    lastIndex = index + 1
    html += escape
  }

  return lastIndex !== index ? html + str.substring(lastIndex, index) : html
}

/**
 * The string form of a regular expression that would match all of the
 * letters, combining marks, and decimal number chars in the unicode character
 * set within a URL.
 */
const alphaNumericAndMarksChars = 'a-z0-9\\-+&@#/%=~_'

/**
 * Partial regex pattern to match the TLD of a domain.
 *
 * Maximum length for a TLD is currently 24 characters.
 * See: as shown in http://data.iana.org/TLD/tlds-alpha-by-domain.txt
 */
const tldPattern = '[a-z]{2,24}'

/**
 * Partial regex pattern to match the domain part of a URL without the subdomain.
 */
const domainPattern = '[a-z0-9-]+\\.' + tldPattern

/**
 * Partial regex pattern to match the path of a URL.
 */
const pathPattern =
  '(?:[/?#](?:[' +
  alphaNumericAndMarksChars +
  "\\(\\)|'$*\\[\\]?!:,.;]*[" +
  alphaNumericAndMarksChars +
  "|'$*\\[\\]])?)?)"

/**
 * Regex pattern to match a complete URL.
 */
const urlPattern =
  '(?:(?:(?:https?:\\/\\/(?:[a-z0-9-]+\\.)*)|(?:[a-z0-9-]+\\.)+)?' +
  domainPattern +
  pathPattern

/**
 * Regex pattern to match an email address.
 */
const emailPattern = "(?:\\b[a-z0-9._'%+-]+@[a-z0-9.-]+\\." + tldPattern + ')'

/**
 * @param {string} str
 *
 * @returns {string}
 */
export const convertLinksToHTML = (str?: string) => {
  if (str === undefined) {
    return ''
  }

  return str
    .split(new RegExp(`(${urlPattern}|${emailPattern})`, 'gi'))
    .reduce((accumulator, value, index) => {
      if (index % 2) {
        if (value.match(new RegExp(`^${emailPattern}$`, 'i'))) {
          // Matched an email
          return (
            accumulator +
            `<a href="mailto:${escapeHTML(value)}">${escapeHTML(value)}</a>`
          )
        }

        // Matched a URL
        let url = value

        if (url.match(new RegExp(`^${domainPattern}$`, 'i'))) {
          // Only matched a domain name (without subdomain)
          // Skip this as it could be the end/start of a sentence without whitespace.
          // For example with "Hello Tom.how are you?" we should not match "Tom.how"
          return accumulator + url
        }

        // Add http as the default scheme if needed
        url = normalizeUrl(url)

        // Adding target blank and rel noopener for external links
        // See: https://developers.google.com/web/tools/lighthouse/audits/noopener
        return (
          accumulator +
          `<a href="${escapeHTML(
            url
          )}" target="_blank" rel="noopener">${escapeHTML(value)}</a>`
        )
      }

      return accumulator + escapeHTML(value)
    }, '')
}

/**
 * Camelcases a specified string.
 *
 * @param   {string} str The string.
 * @returns {string} The camelCased string.
 */
export function camelCase(str?: string) {
  if (str === undefined) {
    return ''
  }

  return str
    .replace(/-/g, ' ')
    .replace(/(?:^\w|[A-Z]|\b\w|\s+)/g, (match, index) => {
      if (+match === 0) return '' // or if (/\s+/.test(match)) for white spaces
      return index === 0 ? match.toLowerCase() : match.toUpperCase()
    })
}

/**
 * @license
 * Copyright Google Inc. All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */

const STRING_DASHERIZE_REGEXP = /[ _.]/g
const STRING_DECAMELIZE_REGEXP = /([a-z\d])([A-Z])/g

/**
   * Converts a camelized string into all lower case separated by underscores.
   *
   ```javascript
   decamelize('innerHTML');         // 'inner_html'
   decamelize('action_name');       // 'action_name'
   decamelize('css-class-name');    // 'css-class-name'
   decamelize('my favorite items'); // 'my favorite items'
   ```

   @method decamelize
   @param {String} str The string to decamelize.
   @return {String} the decamelized string.
   */
export function decamelize(str?: string) {
  if (str === undefined) {
    return ''
  }

  return str.replace(STRING_DECAMELIZE_REGEXP, '$1_$2').toLowerCase()
}

/**
   Replaces underscores, spaces, periods, or camelCase with dashes.

   ```javascript
   dasherize('innerHTML');         // 'inner-html'
   dasherize('action_name');       // 'action-name'
   dasherize('css-class-name');    // 'css-class-name'
   dasherize('my favorite items'); // 'my-favorite-items'
   dasherize('nrwl.io');           // 'nrwl-io'
   ```

   @method dasherize
   @param {String} str The string to dasherize.
   @return {String} the dasherized string.
   */
export function dasherize(str?: string) {
  if (str === undefined) {
    return ''
  }

  return decamelize(str || '').replace(STRING_DASHERIZE_REGEXP, '-')
}

export function capitalizeFirstLetter(str?: string) {
  if (str === undefined) {
    return ''
  }

  return str.charAt(0).toUpperCase() + str.slice(1)
}

/**
 * A super tiny, but naive, way to pluralize a word based on a count value.
 * @param word The word to pluralize.
 * @param count The count to check against.
 * @returns The pluralized word.
 */
export function pluralize(word?: string, count: number = 1): string {
  if (!word) return ''
  if (word.lastIndexOf('s') === word.length - 1) return word
  return count === 1 ? word : `${word}s`
}

/**
 * Decodes HTML entities like &lt;, &gt;, &amp;, etc. back into plain characters.
 * Useful when you want to preserve the appearance of invalid HTML tags as raw text.
 *
 * @param {string} str - The string containing HTML entities.
 * @returns {string} - The decoded string.
 */
export function decodeHTMLEntities(str?: string): string {
  if (str === undefined) return ''

  const doc = new DOMParser().parseFromString(
    DOMPurify.sanitize(str),
    'text/html'
  )
  const decoded = doc.documentElement.textContent || ''

  return decoded
    .replace(/&nbsp;/g, ' ')
    .replace(/&amp;/g, '&')
    .replace(/&lt;/g, '<')
    .replace(/&gt;/g, '>')
}

/**
 * Sanitizes HTML while preserving tag-like content as visible plain text.
 * For example, "<tag>hello</tag>" becomes "&lt;tag&gt;hello&lt;/tag&gt;" before sanitization,
 * and is decoded back to "<tag>hello</tag>" afterward.
 *
 * @param {string} str - The input HTML string.
 * @returns {string} - A sanitized, safe-to-render string that still shows invalid tags as text.
 */
export function sanitizePreservingTags(str?: string): string {
  if (!str) return ''

  const encoded = str.replace(/</g, '&lt;').replace(/>/g, '&gt;')

  const sanitized = DOMPurify.sanitize(encoded)

  return decodeHTMLEntities(sanitized)
}
