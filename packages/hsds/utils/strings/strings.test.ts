import {
  convertLinksToHTML,
  decodeHTMLEntities,
  escapeHTML,
  isValidUrl,
  newlineToHTML,
  normalizeUrl,
  sanitizePreservingTags,
} from '.'

describe('normalizeUrl', () => {
  test('returns an empty string if nothing is provided', () => {
    expect(normalizeUrl()).toEqual('')
  })

  test('Adds the "http://" scheme if necessary', () => {
    const urls = [
      ['www.example.com', 'http://www.example.com'],
      ['http://www.example.com', 'http://www.example.com'],
      ['https://www.example.com', 'https://www.example.com'],
    ]

    urls.forEach(fixture => {
      expect(normalizeUrl(fixture[0])).toEqual(fixture[1])
    })
  })
})

describe('newlineToHTML', () => {
  test('Returns string, untouched, if there are no newlines', () => {
    const string = 'word1 word2'
    expect(newlineToHTML(string)).toEqual(string)
  })

  test('Replaces newline with <br /> tag', () => {
    const string = 'word1\nword2'
    expect(newlineToHTML(string)).toEqual('word1<br>word2')
  })

  test('Replaces multiple newline with multiple <br /> tag', () => {
    const string = 'word1\nword2\nword3'
    expect(newlineToHTML(string)).toEqual('word1<br>word2<br>word3')
  })
})

describe('escapeHTML', () => {
  it('should not escape non HTML characters', () => {
    expect(escapeHTML('This contains no HTML')).toEqual('This contains no HTML')
  })

  it('should escape HTML tags', () => {
    expect(escapeHTML('<p>Hello</p>')).toEqual('&lt;p&gt;Hello&lt;/p&gt;')
  })

  it('should escape double quotes', () => {
    expect(escapeHTML('"Double quoted"')).toEqual('&quot;Double quoted&quot;')
  })

  it('should escape single quotes', () => {
    expect(escapeHTML("'Single quoted'")).toEqual('&#x27;Single quoted&#x27;')
  })

  it('should escape ampersands', () => {
    expect(escapeHTML('This & that')).toEqual('This &amp; that')
  })
})

describe('convertLinksToHTML', () => {
  const withUrls = [
    [
      'www.example.com',
      '<a href="http://www.example.com" target="_blank" rel="noopener">www.example.com</a>',
    ],
    [
      'http://www.example.com/',
      '<a href="http://www.example.com/" target="_blank" rel="noopener">http://www.example.com/</a>',
    ],
    [
      'https://www.example.com/',
      '<a href="https://www.example.com/" target="_blank" rel="noopener">https://www.example.com/</a>',
    ],
    [
      'http://www.example.com',
      '<a href="http://www.example.com" target="_blank" rel="noopener">http://www.example.com</a>',
    ],
    [
      'www.example.com/',
      '<a href="http://www.example.com/" target="_blank" rel="noopener">www.example.com/</a>',
    ],
    [
      'www.example.engineering/example',
      '<a href="http://www.example.engineering/example" target="_blank" rel="noopener">www.example.engineering/example</a>',
    ],
    [
      'www.example.com/example/',
      '<a href="http://www.example.com/example/" target="_blank" rel="noopener">www.example.com/example/</a>',
    ],
    [
      'http://www.example.com/example',
      '<a href="http://www.example.com/example" target="_blank" rel="noopener">http://www.example.com/example</a>',
    ],
    [
      'http://www.example.marketing/example/',
      '<a href="http://www.example.marketing/example/" target="_blank" rel="noopener">http://www.example.marketing/example/</a>',
    ],
    [
      'www.example.com　',
      '<a href="http://www.example.com" target="_blank" rel="noopener">www.example.com</a>　',
    ],

    // Skipping this test due to updating the regex so we don't match every possible letter character.
    // The large regex string was causing some bundle size problems.
    //
    // [
    //   'Линк: https://ru.wikipedia.org/wiki/Футбол',
    //   'Линк: <a href="https://ru.wikipedia.org/wiki/Футбол" target="_blank" rel="noopener">https://ru.wikipedia.org/wiki/Футбол</a>',
    // ],
    [
      '(http://www.example.com/)',
      '(<a href="http://www.example.com/" target="_blank" rel="noopener">http://www.example.com/</a>)',
    ],
    [
      'http://www.example.com/)',
      '<a href="http://www.example.com/" target="_blank" rel="noopener">http://www.example.com/</a>)',
    ],
    [
      'www.example.cool/)',
      '<a href="http://www.example.cool/" target="_blank" rel="noopener">www.example.cool/</a>)',
    ],
    [
      'website:http://www.example.com/example',
      'website:<a href="http://www.example.com/example" target="_blank" rel="noopener">http://www.example.com/example</a>',
    ],
    [
      'http://www.example.com/example-example/.',
      '<a href="http://www.example.com/example-example/" target="_blank" rel="noopener">http://www.example.com/example-example/</a>.',
    ],
    [
      'http://www.example.com/example-example/-',
      '<a href="http://www.example.com/example-example/-" target="_blank" rel="noopener">http://www.example.com/example-example/-</a>',
    ],
    [
      'www.example.com/example?fdasfs=24fa3fd32',
      '<a href="http://www.example.com/example?fdasfs=24fa3fd32" target="_blank" rel="noopener">www.example.com/example?fdasfs=24fa3fd32</a>',
    ],
    [
      'www.example.com/example?fdasfs=24fa3fd32&asdsa=af5t34tw',
      '<a href="http://www.example.com/example?fdasfs=24fa3fd32&amp;asdsa=af5t34tw" target="_blank" rel="noopener">www.example.com/example?fdasfs=24fa3fd32&amp;asdsa=af5t34tw</a>',
    ],
    [
      'http://www.example.com/example?fdasfs=24fa3fd32',
      '<a href="http://www.example.com/example?fdasfs=24fa3fd32" target="_blank" rel="noopener">http://www.example.com/example?fdasfs=24fa3fd32</a>',
    ],
    [
      'http://www.example.com/example?fdasfs=24fa3fd32&asdsa=af5t34tw',
      '<a href="http://www.example.com/example?fdasfs=24fa3fd32&amp;asdsa=af5t34tw" target="_blank" rel="noopener">http://www.example.com/example?fdasfs=24fa3fd32&amp;asdsa=af5t34tw</a>',
    ],
    [
      '(www-example.example.example.com).',
      '(<a href="http://www-example.example.example.com" target="_blank" rel="noopener">www-example.example.example.com</a>).',
    ],
    [
      'http://www.example.com/example.aspx',
      '<a href="http://www.example.com/example.aspx" target="_blank" rel="noopener">http://www.example.com/example.aspx</a>',
    ],
    [
      '(www.example.com/example.php)',
      '(<a href="http://www.example.com/example.php" target="_blank" rel="noopener">www.example.com/example.php</a>)',
    ],
    [
      '.http://www.example.com',
      '.<a href="http://www.example.com" target="_blank" rel="noopener">http://www.example.com</a>',
    ],
    [
      '/http://www.example.com',
      '/<a href="http://www.example.com" target="_blank" rel="noopener">http://www.example.com</a>',
    ],
    [
      '"http://www.example.com"',
      '&quot;<a href="http://www.example.com" target="_blank" rel="noopener">http://www.example.com</a>&quot;',
    ],
    [
      "'http://www.example.com'",
      '&#x27;<a href="http://www.example.com" target="_blank" rel="noopener">http://www.example.com</a>&#x27;',
    ],
    [
      '"example.com/"',
      '&quot;<a href="http://example.com/" target="_blank" rel="noopener">example.com/</a>&quot;',
    ],
    [
      'http://mail.example.com/example/compose?to=<EMAIL>',
      '<a href="http://mail.example.com/example/compose?to=<EMAIL>" target="_blank" rel="noopener">http://mail.example.com/example/compose?to=<EMAIL></a>',
    ],
    [
      'http://example.com/something?co,m,m,as,',
      '<a href="http://example.com/something?co,m,m,as" target="_blank" rel="noopener">http://example.com/something?co,m,m,as</a>,',
    ],
    [
      'example.com/',
      '<a href="http://example.com/" target="_blank" rel="noopener">example.com/</a>',
    ],
    [
      'example.com/example/example',
      '<a href="http://example.com/example/example" target="_blank" rel="noopener">example.com/example/example</a>',
    ],
    [
      'example.example.com/',
      '<a href="http://example.example.com/" target="_blank" rel="noopener">example.example.com/</a>',
    ],
    [
      'example.example.com/example/example',
      '<a href="http://example.example.com/example/example" target="_blank" rel="noopener">example.example.com/example/example</a>',
    ],
    [
      'example.com/example#example',
      '<a href="http://example.com/example#example" target="_blank" rel="noopener">example.com/example#example</a>',
    ],
    [
      'http://example.com/example#example',
      '<a href="http://example.com/example#example" target="_blank" rel="noopener">http://example.com/example#example</a>',
    ],
    [
      'www.example.com/example.pdf',
      '<a href="http://www.example.com/example.pdf" target="_blank" rel="noopener">www.example.com/example.pdf</a>',
    ],
    [
      'www-example.example.com.',
      '<a href="http://www-example.example.com" target="_blank" rel="noopener">www-example.example.com</a>.',
    ],
    [
      'http://ex.am/pLE',
      '<a href="http://ex.am/pLE" target="_blank" rel="noopener">http://ex.am/pLE</a>',
    ],
    [
      'http://www.example.com/~example/example.html',
      '<a href="http://www.example.com/~example/example.html" target="_blank" rel="noopener">http://www.example.com/~example/example.html</a>',
    ],
    [
      'examplehttp://www.example.com',
      'example<a href="http://www.example.com" target="_blank" rel="noopener">http://www.example.com</a>',
    ],
    [
      'examplewww.example.com/',
      '<a href="http://examplewww.example.com/" target="_blank" rel="noopener">examplewww.example.com/</a>',
    ],
    [
      '<p>www.example.com</p>',
      '&lt;p&gt;<a href="http://www.example.com" target="_blank" rel="noopener">www.example.com</a>&lt;/p&gt;',
    ],
    [
      'www.example.com www.helpscout.com',
      '<a href="http://www.example.com" target="_blank" rel="noopener">www.example.com</a> <a href="http://www.helpscout.com" target="_blank" rel="noopener">www.helpscout.com</a>',
    ],
    [
      '<EMAIL>',
      '<a href="mailto:<EMAIL>"><EMAIL></a>',
    ],
    [
      "example.a'<EMAIL>",
      '<a href="mailto:example.a&#x27;<EMAIL>">example.a&#x27;<EMAIL></a>',
    ],
    [
      '<EMAIL>',
      '<a href="mailto:<EMAIL>"><EMAIL></a>',
    ],
    [
      '<EMAIL>',
      '<a href="mailto:<EMAIL>"><EMAIL></a>',
    ],
    [
      '<EMAIL>',
      '<a href="mailto:<EMAIL>"><EMAIL></a>',
    ],
    [
      '<EMAIL>',
      '<a href="mailto:<EMAIL>"><EMAIL></a>',
    ],
    [
      'Lorem <EMAIL> ipsum.',
      'Lorem <a href="mailto:<EMAIL>"><EMAIL></a> ipsum.',
    ],
    [
      '<NAME_EMAIL> dolor sit amet.',
      'Lorem ipsum <a href="mailto:<EMAIL>"><EMAIL></a> dolor sit amet.',
    ],
    [
      '<EMAIL>.',
      '<a href="mailto:<EMAIL>"><EMAIL></a>.',
    ],
    [
      '<EMAIL>,',
      '<a href="mailto:<EMAIL>"><EMAIL></a>,',
    ],
    [
      '<EMAIL>.',
      '<a href="mailto:<EMAIL>"><EMAIL></a>.',
    ],
    [
      '<EMAIL>　',
      '<a href="mailto:<EMAIL>"><EMAIL></a>　',
    ],

    // Skipping this test due to updating the regex so we don't match every possible letter character.
    // The large regex string was causing some bundle size problems.
    //
    // [
    //   'Привет <EMAIL>!',
    //   'Привет <a href="mailto:<EMAIL>"><EMAIL></a>!',
    // ],

    [
      '(<EMAIL>)',
      '(<a href="mailto:<EMAIL>"><EMAIL></a>)',
    ],
    [
      '(<EMAIL>)',
      '(<a href="mailto:<EMAIL>"><EMAIL></a>)',
    ],
    [
      '(<EMAIL>),',
      '(<a href="mailto:<EMAIL>"><EMAIL></a>),',
    ],
    [
      '(<EMAIL>).',
      '(<a href="mailto:<EMAIL>"><EMAIL></a>).',
    ],
    [
      '<EMAIL>).',
      '<a href="mailto:<EMAIL>"><EMAIL></a>).',
    ],
    [
      '(<EMAIL>',
      '(<a href="mailto:<EMAIL>"><EMAIL></a>',
    ],
    [
      'mailto:<EMAIL>',
      'mailto:<a href="mailto:<EMAIL>"><EMAIL></a>',
    ],
    [
      '(mailto:<EMAIL>)or',
      '(mailto:<a href="mailto:<EMAIL>"><EMAIL></a>)or',
    ],
    [
      'Email:<EMAIL>',
      'Email:<a href="mailto:<EMAIL>"><EMAIL></a>',
    ],
    [
      'http://example.com?email=<EMAIL>',
      '<a href="http://example.com?email=<EMAIL>" target="_blank" rel="noopener">http://example.com?email=<EMAIL></a>',
    ],
    [
      '<EMAIL>',
      '<a href="mailto:<EMAIL>"><EMAIL></a>',
    ],
    [
      '<EMAIL>',
      '<a href="mailto:<EMAIL>"><EMAIL></a>',
    ],
    [
      '<EMAIL>',
      '<a href="mailto:<EMAIL>"><EMAIL></a>',
    ],
    [
      '<EMAIL>',
      '<a href="mailto:<EMAIL>"><EMAIL></a>ABGSDGA',
    ],
    [
      'www.example.engineering',
      '<a href="http://www.example.engineering" target="_blank" rel="noopener">www.example.engineering</a>',
    ],
    [
      'www.example.designer',
      '<a href="http://www.example.designer" target="_blank" rel="noopener">www.example.designer</a>',
    ],
    [
      'www.example.XNAAVERMGENSBERATUNGAPWB',
      '<a href="http://www.example.XNAAVERMGENSBERATUNGAPWB" target="_blank" rel="noopener">www.example.XNAAVERMGENSBERATUNGAPWB</a>',
    ],
    [
      'www.example.XNAAVERMGENSBERATUNGAPWBABGSDGA',
      '<a href="http://www.example.XNAAVERMGENSBERATUNGAPWB" target="_blank" rel="noopener">www.example.XNAAVERMGENSBERATUNGAPWB</a>ABGSDGA',
    ],
    [
      'http://www.test./path',
      '<a href="http://www.test" target="_blank" rel="noopener">http://www.test</a>./path',
    ],
  ]

  withUrls.forEach(fixture => {
    it(`should autolink URLs in "${fixture[0]}"`, () => {
      expect(convertLinksToHTML(fixture[0])).toEqual(fixture[1])
    })
  })

  const withoutUrls = [
    'Lorem ipsum dolor sit amet.',
    'Lorem ipsum dolor.sit amet.',
    'Lorem ipsum dolor@sit amet',
    'Lorem ipsum 8.15am dolor 4.30pm.',
    'Lorem:ipsum',
    'Lorem/ipsum',
    'Lorem://ipsum',
    'Lorem://ipsum/dolor',
    'helpscout.com',
    'www.test./path',
    'http://www.',
  ]

  withoutUrls.forEach(fixture => {
    it(`should not autolink non URLs "${fixture}"`, () => {
      expect(convertLinksToHTML(fixture)).toEqual(fixture)
    })
  })
})

describe('isValidUrl', () => {
  // Valid URLs
  test('should return true for valid http URLs', () => {
    expect(isValidUrl('http://example.com')).toBe(true)
    expect(isValidUrl('http://www.example.com')).toBe(true)
    expect(isValidUrl('http://subdomain.example.com')).toBe(true)
    expect(isValidUrl('http://example.com/path')).toBe(true)
    expect(isValidUrl('http://example.com/path/to/resource')).toBe(true)
    expect(isValidUrl('http://example.com:8080')).toBe(true)
    expect(isValidUrl('http://example.com?query=param')).toBe(true)
    expect(isValidUrl('http://example.com#fragment')).toBe(true)
    expect(isValidUrl('http://***************')).toBe(true)
  })

  test('should return true for valid https URLs', () => {
    expect(isValidUrl('https://example.com')).toBe(true)
    expect(isValidUrl('https://www.example.com')).toBe(true)
    expect(isValidUrl('https://example.com/path?query=param#fragment')).toBe(
      true
    )
  })

  test('should return true for URLs with special characters in path', () => {
    expect(isValidUrl('https://example.com/path with spaces')).toBe(true)
    expect(isValidUrl('https://example.com/path-with-dashes')).toBe(true)
    expect(isValidUrl('https://example.com/path_with_underscores')).toBe(true)
    expect(isValidUrl('https://example.com/path/with/slashes')).toBe(true)
  })

  // Invalid URLs
  test('should return false for null or empty string', () => {
    expect(isValidUrl('')).toBe(false)
    expect(isValidUrl(null as unknown as string)).toBe(false)
    expect(isValidUrl(undefined as unknown as string)).toBe(false)
  })

  test('should return false for strings without protocol', () => {
    expect(isValidUrl('example.com')).toBe(false)
    expect(isValidUrl('www.example.com')).toBe(false)
  })

  test('should return false for invalid protocols', () => {
    expect(isValidUrl('ftp://example.com')).toBe(false)
    expect(isValidUrl('ssh://example.com')).toBe(false)
    expect(isValidUrl('file:///C:/path/to/file')).toBe(false)
    expect(isValidUrl('javascript:alert(1)')).toBe(false)
  })

  test('should return false for malformed URLs', () => {
    expect(isValidUrl('http:/example.com')).toBe(false)
    expect(isValidUrl('https://example..com')).toBe(false)
    expect(isValidUrl('http://.com')).toBe(false)
  })

  test('should return false for non-URL strings', () => {
    expect(isValidUrl('This is not a URL')).toBe(false)
    expect(isValidUrl('123456')).toBe(false)
    expect(isValidUrl('http://')).toBe(false)
  })
})

describe('decodeHTMLEntities', () => {
  test('returns empty string if nothing is provided', () => {
    expect(decodeHTMLEntities()).toEqual('')
  })

  test('decodes basic HTML entities', () => {
    expect(decodeHTMLEntities('&lt;div&gt;')).toEqual('<div>')
    expect(decodeHTMLEntities('&amp;')).toEqual('&')
    expect(decodeHTMLEntities('&gt;')).toEqual('>')
    expect(decodeHTMLEntities('&quot;')).toEqual('"')
    expect(decodeHTMLEntities('&#x27;')).toEqual("'")
    // Non-breaking space might be decoded differently depending on the implementation
    // Check that it's decoded to some kind of space character
    expect(decodeHTMLEntities('&nbsp;').trim()).toEqual('')
  })

  test('decodes multiple entities in a string', () => {
    expect(
      decodeHTMLEntities('&lt;div&gt;Hello &amp; world&lt;/div&gt;')
    ).toEqual('<div>Hello & world</div>')
  })

  test('handles strings with no entities', () => {
    const str = 'Hello world'
    expect(decodeHTMLEntities(str)).toEqual(str)
  })

  test('safely handles potentially malicious content', () => {
    const malicious = '&lt;script&gt;alert("XSS")&lt;/script&gt;'
    expect(decodeHTMLEntities(malicious)).toEqual(
      '<script>alert("XSS")</script>'
    )
  })
})

describe('sanitizePreservingTags', () => {
  test('returns empty string if nothing is provided', () => {
    expect(sanitizePreservingTags()).toEqual('')
    expect(sanitizePreservingTags('')).toEqual('')
    expect(sanitizePreservingTags(null as unknown as string)).toEqual('')
  })

  test('preserves HTML-like tags as visible text', () => {
    expect(sanitizePreservingTags('<div>Hello</div>')).toEqual(
      '<div>Hello</div>'
    )
    expect(sanitizePreservingTags('<script>alert("XSS")</script>')).toEqual(
      '<script>alert("XSS")</script>'
    )
  })

  test('preserves nested tags as visible text', () => {
    expect(sanitizePreservingTags('<div><span>Nested</span></div>')).toEqual(
      '<div><span>Nested</span></div>'
    )
  })

  test('handles strings with special characters', () => {
    expect(sanitizePreservingTags('Hello & world')).toEqual('Hello & world')
    expect(sanitizePreservingTags('Quote "test" & <tag>')).toEqual(
      'Quote "test" & <tag>'
    )
  })

  test('preserves HTML entities in the text', () => {
    expect(sanitizePreservingTags('&lt;already encoded&gt;')).toEqual(
      '<already encoded>'
    )
  })

  test('handles mixed content correctly', () => {
    expect(
      sanitizePreservingTags(
        '<div>Hello</div> & <script>alert("test")</script>'
      )
    ).toEqual('<div>Hello</div> & <script>alert("test")</script>')
  })
})
