import React, { useCallback, useEffect } from 'react'

import cx from 'classnames'
import { useContextualToolbarContext } from 'editor/plugins/ContextualToolbar/hooks/useContextualToolbar'
import { AutocompleteContext, useHsEditorContext } from 'editor/utils/hooks'
import { useHsEditorFixedToolbarContext } from 'editor/utils/hooks/useHsEditorFixedToolbar'
import { PortalBody } from 'editor/utils/portal'
import { selectAll } from 'editor/utils/selection'
import { Range } from 'slate'
import { useSlate } from 'slate-react'

import BaseIcon from 'hsds/icons/baseIcon'

import { useClickOutside } from '../../hooks/useClickOutside'
import { useCloseOtherDropdown } from '../../hooks/useCloseOtherDropdown'
import ToolbarButton from '../ToolbarButton/ToolbarButton'
import {
  TOOLBAR_BUTTON_DROPDOWN_TEST_ID,
  TOOLBAR_BUTTON_ICON_SVG,
  TOOLBAR_BUTTON_LABEL,
  TOOLBAR_BUTTON_TEST_ID,
} from './AIAssistDropdown.constants'
import { AutocompleteUI, ItemUI } from './AIAssistDropdown.css'
import { useAIAssistDropdown } from './AIAssistDropdown.hooks'
import { AIAssistDropdownSearch } from './AIAssistDropdownSearch'

import { ItemListUI } from 'editor/components/Autocomplete/ItemList/ItemList.css'

const Icon = props => <BaseIcon {...props} svg={TOOLBAR_BUTTON_ICON_SVG} />

const AIAssistDropdown = props => {
  const editor = useSlate()

  const { setFocusRingVisibility } = useHsEditorContext()
  const {
    fixedToolbarRef,
    toolbarRef,
    openDropdown,
    closeDropdown,
    isVisible,
  } = useContextualToolbarContext()
  const { isFixedToolbar } = useHsEditorFixedToolbarContext()
  const {
    isMounted,
    isOpen,
    setDropdownVisible,
    filteredItems,
    handleKeyDown,
    highlightItemIndex,
    handleSearch,
    showSearch,
    selectItem,
    handleItemMouseDown,
    contextValue,
    setReference,
    setFloating,
    status,
    refs,
  } = useAIAssistDropdown()

  const autocompleteProps = {
    ref: setFloating,
    'aria-label': TOOLBAR_BUTTON_LABEL,
    isOpen,
    className: cx(status && `is-${status}`),
  }

  const handleClick = useCallback(
    e => {
      e.preventDefault()
      e.stopPropagation()

      const newState = !isOpen

      if (
        isFixedToolbar &&
        newState === true &&
        Range.isCollapsed(editor.selection)
      ) {
        selectAll(editor)
      }

      newState ? openDropdown() : closeDropdown()
      setDropdownVisible(newState)
    },
    [
      closeDropdown,
      openDropdown,
      setDropdownVisible,
      isOpen,
      editor,
      isFixedToolbar,
    ],
  )

  const handleClickOutside = useCallback(() => {
    closeDropdown()
    setDropdownVisible(false)
  }, [closeDropdown, setDropdownVisible])

  const { ref: itemListRef } = useClickOutside(handleClickOutside, [
    refs.reference,
  ])

  useCloseOtherDropdown({
    toolbarRef,
    fixedToolbarRef,
    referenceRef: refs.reference,
    handleClickOutside,
    isOpen,
  })

  useEffect(() => {
    if (!isVisible) setDropdownVisible(false)
  }, [isVisible, setDropdownVisible])

  const isDisabled = editor.isEditorEmpty() && isFixedToolbar

  return (
    <>
      <ToolbarButton
        icon={Icon}
        size="md"
        onClick={handleClick}
        ref={setReference}
        tooltipTitle={TOOLBAR_BUTTON_LABEL}
        aria-label={TOOLBAR_BUTTON_LABEL}
        aria-haspopup="true"
        aria-expanded={isOpen}
        shouldShowTooltip={!isOpen}
        data-cy={TOOLBAR_BUTTON_TEST_ID}
        isActive={isOpen}
        disabled={isDisabled}
        {...props}
      />
      <PortalBody>
        <AutocompleteContext.Provider value={contextValue}>
          <AutocompleteUI
            data-cy={TOOLBAR_BUTTON_DROPDOWN_TEST_ID}
            onKeyDown={handleKeyDown}
            onMouseDown={e => {
              e.preventDefault()
              e.stopPropagation()
            }}
            onFocus={() =>
              setFocusRingVisibility && setFocusRingVisibility(true)
            }
            tabIndex="0"
            {...autocompleteProps}
          >
            {isMounted && (
              <>
                {showSearch && (
                  <AIAssistDropdownSearch onChange={handleSearch} />
                )}
                <ItemListUI ref={itemListRef}>
                  {filteredItems?.map((item, index) => {
                    return (
                      <ItemUI
                        className={cx(
                          item.isBack && 'backButton',
                          'is-mouse-active',
                        )}
                        subtitle={item.subtitle}
                        key={item.id}
                        isSelected={item.isSelected}
                        isHighlighted={item.index === highlightItemIndex}
                        onClick={e => {
                          selectItem(e, item.index)
                        }}
                        index={index}
                        onMouseDown={handleItemMouseDown}
                        image={false}
                        withAction={false}
                      />
                    )
                  })}
                </ItemListUI>
              </>
            )}
          </AutocompleteUI>
        </AutocompleteContext.Provider>
      </PortalBody>
    </>
  )
}

export default React.memo(AIAssistDropdown)
